#!/bin/bash

# Script to deploy fixes to the live ZARA-Events application
echo "🚀 Deploying ZARA-Events Fixes to Google Cloud Run"
echo "=================================================="

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Step 1: Building updated Docker image...${NC}"

# Build the Docker image with fixes
docker build -t zaramillion/zara-events:latest .

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Docker image built successfully${NC}"
else
    echo -e "${RED}❌ Docker build failed${NC}"
    exit 1
fi

echo ""
echo -e "${YELLOW}Step 2: Pushing image to Docker Hub...${NC}"

# Push to Docker Hub
docker push zaramillion/zara-events:latest

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Image pushed to Docker Hub successfully${NC}"
else
    echo -e "${RED}❌ Docker push failed${NC}"
    exit 1
fi

echo ""
echo -e "${YELLOW}Step 3: Deploying to Google Cloud Run...${NC}"

# Deploy to Cloud Run (this will pull the latest image)
gcloud run deploy zara-events \
    --image=zaramillion/zara-events:latest \
    --platform=managed \
    --region=us-central1 \
    --allow-unauthenticated \
    --port=80 \
    --memory=1Gi \
    --set-env-vars="ENVIRONMENT=production"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Deployment successful${NC}"
else
    echo -e "${RED}❌ Deployment failed${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}🎉 Deployment Complete!${NC}"
echo ""
echo "Next steps:"
echo "1. Go to: https://zara-events-948831228742.us-central1.run.app/test-db-connection.php"
echo "2. Click 'Initialize Database Now' button"
echo "3. Test registration and login functionality"
echo ""
echo "Test credentials after initialization:"
echo "• Admin: admin / admin123"
echo "• User: testuser / user123"
