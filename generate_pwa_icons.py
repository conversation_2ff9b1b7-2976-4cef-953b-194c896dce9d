#!/usr/bin/env python3
"""
PWA Icon Generator for ZARA-Events
Creates all required PWA icons from a base design
"""

import os
from PIL import Image, ImageDraw, ImageFont
import sys

def create_base_icon(size=512):
    """Create a base icon with ZARA-Events branding"""
    
    # Create image with transparent background
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Colors
    primary_color = (64, 134, 129)  # #408681
    secondary_color = (251, 241, 223)  # #FBF1DF
    white = (255, 255, 255)
    
    # Create circular background
    margin = size // 10
    circle_size = size - (2 * margin)
    
    # Draw gradient-like effect with multiple circles
    for i in range(5):
        offset = i * 3
        color_intensity = 1 - (i * 0.15)
        circle_color = tuple(int(c * color_intensity) for c in primary_color) + (255,)
        
        draw.ellipse([
            margin + offset, 
            margin + offset, 
            size - margin - offset, 
            size - margin - offset
        ], fill=circle_color)
    
    # Add calendar icon
    calendar_size = size // 3
    calendar_x = (size - calendar_size) // 2
    calendar_y = (size - calendar_size) // 2 - size // 20
    
    # Calendar body
    draw.rectangle([
        calendar_x, 
        calendar_y + calendar_size // 6,
        calendar_x + calendar_size,
        calendar_y + calendar_size
    ], fill=white)
    
    # Calendar header
    draw.rectangle([
        calendar_x, 
        calendar_y,
        calendar_x + calendar_size,
        calendar_y + calendar_size // 3
    ], fill=secondary_color)
    
    # Calendar rings
    ring_width = calendar_size // 20
    ring_height = calendar_size // 8
    ring_y = calendar_y - ring_height // 2
    
    for ring_x in [calendar_x + calendar_size // 4, calendar_x + 3 * calendar_size // 4]:
        draw.rectangle([
            ring_x - ring_width // 2,
            ring_y,
            ring_x + ring_width // 2,
            ring_y + ring_height
        ], fill=primary_color)
    
    # Add text if size is large enough
    if size >= 192:
        try:
            # Try to use a system font
            font_size = size // 20
            font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", font_size)
        except:
            try:
                font = ImageFont.truetype("arial.ttf", font_size)
            except:
                font = ImageFont.load_default()
        
        text = "ZARA"
        text_bbox = draw.textbbox((0, 0), text, font=font)
        text_width = text_bbox[2] - text_bbox[0]
        text_height = text_bbox[3] - text_bbox[1]
        
        text_x = (size - text_width) // 2
        text_y = calendar_y + calendar_size + size // 20
        
        draw.text((text_x, text_y), text, fill=white, font=font)
    
    return img

def generate_all_icons():
    """Generate all required PWA icons"""
    
    # Icon sizes required for PWA
    sizes = [72, 96, 128, 144, 152, 192, 384, 512]
    
    # Create icons directory
    icons_dir = "assets/images/icons"
    os.makedirs(icons_dir, exist_ok=True)
    
    print("Generating PWA icons...")
    
    for size in sizes:
        print(f"Creating {size}x{size} icon...")
        
        # Create icon
        icon = create_base_icon(size)
        
        # Save icon
        icon_path = os.path.join(icons_dir, f"icon-{size}x{size}.png")
        icon.save(icon_path, "PNG", optimize=True)
        
        print(f"✅ Saved: {icon_path}")
    
    # Create additional icons
    create_additional_icons(icons_dir)
    
    print("\n🎉 All PWA icons generated successfully!")
    print(f"📁 Icons saved in: {icons_dir}")

def create_additional_icons(icons_dir):
    """Create additional icons for shortcuts and badges"""
    
    # Badge icon (smaller, simpler)
    badge = create_base_icon(72)
    badge.save(os.path.join(icons_dir, "badge-72x72.png"), "PNG", optimize=True)
    
    # Shortcut icons
    shortcuts = {
        "shortcut-events.png": "📅",
        "shortcut-dashboard.png": "📊", 
        "shortcut-cart.png": "🛒"
    }
    
    for filename, emoji in shortcuts.items():
        shortcut_icon = create_shortcut_icon(emoji)
        shortcut_icon.save(os.path.join(icons_dir, filename), "PNG", optimize=True)
    
    # Action icons for notifications
    actions = {
        "action-explore.png": "🔍",
        "action-close.png": "❌"
    }
    
    for filename, emoji in actions.items():
        action_icon = create_action_icon(emoji)
        action_icon.save(os.path.join(icons_dir, filename), "PNG", optimize=True)

def create_shortcut_icon(emoji, size=96):
    """Create a shortcut icon with emoji"""
    img = Image.new('RGBA', (size, size), (64, 134, 129, 255))
    draw = ImageDraw.Draw(img)
    
    # Add emoji (simplified as text)
    try:
        font_size = size // 2
        font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", font_size)
    except:
        font = ImageFont.load_default()
    
    text_bbox = draw.textbbox((0, 0), emoji, font=font)
    text_width = text_bbox[2] - text_bbox[0]
    text_height = text_bbox[3] - text_bbox[1]
    
    text_x = (size - text_width) // 2
    text_y = (size - text_height) // 2
    
    draw.text((text_x, text_y), emoji, fill=(255, 255, 255), font=font)
    
    return img

def create_action_icon(emoji, size=48):
    """Create an action icon for notifications"""
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Circular background
    draw.ellipse([0, 0, size, size], fill=(64, 134, 129, 255))
    
    # Add emoji
    try:
        font_size = size // 3
        font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", font_size)
    except:
        font = ImageFont.load_default()
    
    text_bbox = draw.textbbox((0, 0), emoji, font=font)
    text_width = text_bbox[2] - text_bbox[0]
    text_height = text_bbox[3] - text_bbox[1]
    
    text_x = (size - text_width) // 2
    text_y = (size - text_height) // 2
    
    draw.text((text_x, text_y), emoji, fill=(255, 255, 255), font=font)
    
    return img

def create_favicon():
    """Create favicon.ico"""
    icon = create_base_icon(32)
    icon.save("favicon.ico", format="ICO", sizes=[(32, 32)])
    print("✅ Created favicon.ico")

if __name__ == "__main__":
    try:
        generate_all_icons()
        create_favicon()
        
        print("\n📋 Next steps:")
        print("1. The PWA icons have been generated")
        print("2. Update your HTML files to include PWA meta tags")
        print("3. Test the PWA installation on mobile devices")
        print("4. Deploy the updated files to your server")
        
    except ImportError:
        print("❌ PIL (Pillow) is required to generate icons")
        print("Install it with: pip install Pillow")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error generating icons: {e}")
        sys.exit(1)
