<?php
// Start session
session_start();

// Database configuration - Support for Docker, Cloud SQL, and local MySQL
// Check if running in Cloud Run, Docker container, or locally
$isCloudRun = getenv('ENVIRONMENT') === 'production' && getenv('K_SERVICE');
$isDocker = getenv('ENVIRONMENT') === 'production' || file_exists('/.dockerenv');

if ($isCloudRun) {
    // Google Cloud Run configuration
    define('DB_HOST', getenv('DB_HOST') ?: null);
    define('DB_USER', getenv('DB_USER') ?: null);
    define('DB_PASS', getenv('DB_PASS') ?: null);
    define('DB_NAME', getenv('DB_NAME') ?: null);
    define('DB_PORT', getenv('DB_PORT') ?: '3306');
    define('USE_DATABASE', !empty(getenv('DB_HOST')));
} elseif ($isDocker) {
    // Docker configuration
    define('DB_HOST', getenv('DB_HOST') ?: 'mysql:3306');
    define('DB_USER', getenv('DB_USER') ?: 'event_user');
    define('DB_PASS', getenv('DB_PASS') ?: 'event_password');
    define('DB_NAME', getenv('DB_NAME') ?: 'event_booking_system');
    define('DB_PORT', getenv('DB_PORT') ?: '3306');
    define('USE_DATABASE', true);
} else {
    // Local development configuration (connecting to Docker MySQL)
    define('DB_HOST', 'localhost');
    define('DB_USER', 'event_user');
    define('DB_PASS', 'event_password');
    define('DB_NAME', 'event_booking_system');
    define('DB_PORT', '3307'); // Docker exposes MySQL on port 3307
    define('USE_DATABASE', true);
}

// Site configuration
define('SITE_URL', 'http://localhost:8080');
define('SITE_NAME', 'ZARA-Events');
define('ADMIN_EMAIL', '<EMAIL>');

// Security settings
define('SESSION_TIMEOUT', 3600); // 1 hour
define('CSRF_TOKEN_LENGTH', 32);

// File upload settings
define('UPLOAD_DIR', 'assets/images/events/');
define('MAX_FILE_SIZE', 5242880); // 5MB

// Pagination settings
define('EVENTS_PER_PAGE', 12);
define('BOOKINGS_PER_PAGE', 10);

// Currency settings
define('CURRENCY_CODE', 'XAF');
define('CURRENCY_NAME', 'Central African CFA Franc');
define('CURRENCY_SYMBOL', 'XAF');

// Email settings for Gmail SMTP
define('SMTP_HOST', getenv('SMTP_HOST') ?: 'smtp.gmail.com');
define('SMTP_PORT', getenv('SMTP_PORT') ?: 587);
define('SMTP_USERNAME', getenv('SMTP_USERNAME') ?: '<EMAIL>');
define('SMTP_PASSWORD', getenv('SMTP_PASSWORD') ?: 'pvjc rjit ogxg ncce'); // Updated app password
define('SMTP_ENCRYPTION', getenv('SMTP_ENCRYPTION') ?: 'tls');
define('FROM_EMAIL', getenv('FROM_EMAIL') ?: '<EMAIL>');
define('FROM_NAME', getenv('FROM_NAME') ?: 'ZARA-Events');

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Database connection class
class Database {
    private $host = DB_HOST;
    private $user = DB_USER;
    private $pass = DB_PASS;
    private $dbname = DB_NAME;
    private $dbh;
    private $error;
    private $stmt;
    private $connected = false;

    public function __construct() {
        // Only attempt connection if database is configured
        if (!USE_DATABASE || empty($this->host)) {
            $this->connected = false;
            return;
        }

        // Set DSN with port support
        $host_with_port = $this->host;
        if (strpos($this->host, ':') === false) {
            $host_with_port = $this->host . ':' . DB_PORT;
        }
        $dsn = 'mysql:host=' . $host_with_port . ';dbname=' . $this->dbname . ';charset=utf8mb4';

        // Set options
        $options = array(
            PDO::ATTR_PERSISTENT => true,
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        );

        // Create a new PDO instance
        try {
            $this->dbh = new PDO($dsn, $this->user, $this->pass, $options);
            $this->connected = true;
        } catch(PDOException $e) {
            $this->error = $e->getMessage();
            $this->connected = false;
            // In production, log error but don't die
            if (getenv('ENVIRONMENT') === 'production') {
                error_log('Database connection failed: ' . $this->error);
            } else {
                die('Database connection failed: ' . $this->error);
            }
        }
    }

    // Check if database is connected
    public function isConnected() {
        return $this->connected;
    }

    // Get database connection
    public function getConnection() {
        return $this->connected ? $this->dbh : null;
    }

    // Prepare statement with query
    public function query($query) {
        if (!$this->connected) return false;
        $this->stmt = $this->dbh->prepare($query);
    }

    // Bind values
    public function bind($param, $value, $type = null) {
        if (!$this->connected) return false;
        if (is_null($type)) {
            switch (true) {
                case is_int($value):
                    $type = PDO::PARAM_INT;
                    break;
                case is_bool($value):
                    $type = PDO::PARAM_BOOL;
                    break;
                case is_null($value):
                    $type = PDO::PARAM_NULL;
                    break;
                default:
                    $type = PDO::PARAM_STR;
            }
        }
        $this->stmt->bindValue($param, $value, $type);
    }

    // Execute the prepared statement
    public function execute() {
        if (!$this->connected) return false;
        return $this->stmt->execute();
    }

    // Get result set as array of objects
    public function resultset() {
        if (!$this->connected) return [];
        $this->execute();
        return $this->stmt->fetchAll(PDO::FETCH_OBJ);
    }

    // Get single record as object
    public function single() {
        if (!$this->connected) return null;
        $this->execute();
        return $this->stmt->fetch(PDO::FETCH_OBJ);
    }

    // Get row count
    public function rowCount() {
        if (!$this->connected) return 0;
        return $this->stmt->rowCount();
    }

    // Get last insert ID
    public function lastInsertId() {
        if (!$this->connected) return 0;
        return $this->dbh->lastInsertId();
    }
}

// Initialize database connection
$db = new Database();

// Global database availability flag
define('DATABASE_AVAILABLE', $db->isConnected());

// Security functions
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(CSRF_TOKEN_LENGTH));
    }
    return $_SESSION['csrf_token'];
}

function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

function isAdmin() {
    return isLoggedIn() && isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: /auth/login.php');
        exit();
    }
}

function requireAdmin() {
    if (!isAdmin()) {
        header('Location: /index.php');
        exit();
    }
}

// Utility functions
function formatDate($date) {
    return date('F j, Y', strtotime($date));
}

function formatTime($time) {
    return date('g:i A', strtotime($time));
}

function formatCurrency($amount) {
    return number_format($amount, 0) . ' ' . CURRENCY_SYMBOL;
}

function generateBookingReference() {
    return 'BK' . date('Ymd') . strtoupper(substr(uniqid(), -6));
}

function redirect($url) {
    header("Location: $url");
    exit();
}

function setFlashMessage($type, $message) {
    $_SESSION['flash_message'] = [
        'type' => $type,
        'message' => $message
    ];
}

function getFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        unset($_SESSION['flash_message']);
        return $message;
    }
    return null;
}

// Error reporting (disable in production)
$isProduction = getenv('ENVIRONMENT') === 'production';
if ($isProduction) {
    error_reporting(0);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
    ini_set('error_log', 'logs/error.log');
} else {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
}
?>
