<?php
// Demo data for when database is not available
// This provides sample data to showcase the application features

// Demo events data
function getDemoEvents() {
    return [
        (object)[
            'id' => 1,
            'title' => 'Yaoundé Tech Conference 2024',
            'description' => 'Join us for the biggest technology conference in Central Africa. Featuring keynote speakers, workshops, and networking opportunities.',
            'date' => '2024-03-15',
            'time' => '09:00:00',
            'location' => 'Hilton Hotel Yaoundé',
            'address' => 'Boulevard du 20 Mai, Yaoundé, Cameroon',
            'latitude' => 3.8480,
            'longitude' => 11.5021,
            'price' => 25000,
            'capacity' => 500,
            'available_tickets' => 350,
            'image' => 'assets/images/events/tech-conference.jpg',
            'category' => 'Technology',
            'status' => 'active',
            'created_at' => '2024-01-15 10:00:00'
        ],
        (object)[
            'id' => 2,
            'title' => 'Cameroon Music Festival',
            'description' => 'Experience the best of Cameroonian music with local and international artists. A celebration of our rich musical heritage.',
            'date' => '2024-03-22',
            'time' => '18:00:00',
            'location' => 'Palais des Sports de Yaoundé',
            'address' => 'Rue 1.770, Yaoundé, Cameroon',
            'latitude' => 3.8667,
            'longitude' => 11.5167,
            'price' => 15000,
            'capacity' => 2000,
            'available_tickets' => 1500,
            'image' => 'assets/images/events/music-festival.jpg',
            'category' => 'Music',
            'status' => 'active',
            'created_at' => '2024-01-20 14:30:00'
        ],
        (object)[
            'id' => 3,
            'title' => 'Business Networking Breakfast',
            'description' => 'Connect with fellow entrepreneurs and business leaders over breakfast. Share ideas, build partnerships, and grow your network.',
            'date' => '2024-03-08',
            'time' => '07:30:00',
            'location' => 'Djeuga Palace Hotel',
            'address' => 'Rue 1.750, Bastos, Yaoundé, Cameroon',
            'latitude' => 3.8833,
            'longitude' => 11.5167,
            'price' => 8000,
            'capacity' => 100,
            'available_tickets' => 75,
            'image' => 'assets/images/events/business-breakfast.jpg',
            'category' => 'Business',
            'status' => 'active',
            'created_at' => '2024-01-25 09:15:00'
        ],
        (object)[
            'id' => 4,
            'title' => 'Art Exhibition: Modern Cameroon',
            'description' => 'Discover contemporary Cameroonian art featuring works by emerging and established artists from across the country.',
            'date' => '2024-03-30',
            'time' => '15:00:00',
            'location' => 'Goethe Institut Yaoundé',
            'address' => 'Rue 1.061, Bastos, Yaoundé, Cameroon',
            'latitude' => 3.8750,
            'longitude' => 11.5083,
            'price' => 5000,
            'capacity' => 200,
            'available_tickets' => 180,
            'image' => 'assets/images/events/art-exhibition.jpg',
            'category' => 'Arts',
            'status' => 'active',
            'created_at' => '2024-02-01 11:45:00'
        ],
        (object)[
            'id' => 5,
            'title' => 'Startup Pitch Competition',
            'description' => 'Watch innovative startups pitch their ideas to a panel of investors. The winning startup receives funding and mentorship.',
            'date' => '2024-04-05',
            'time' => '14:00:00',
            'location' => 'University of Yaoundé I',
            'address' => 'Ngoa-Ekellé, Yaoundé, Cameroon',
            'latitude' => 3.8667,
            'longitude' => 11.5000,
            'price' => 3000,
            'capacity' => 300,
            'available_tickets' => 250,
            'image' => 'assets/images/events/startup-pitch.jpg',
            'category' => 'Business',
            'status' => 'active',
            'created_at' => '2024-02-05 16:20:00'
        ],
        (object)[
            'id' => 6,
            'title' => 'Food & Culture Festival',
            'description' => 'Taste the flavors of Cameroon and learn about our diverse cultural traditions. Food stalls, cultural performances, and more.',
            'date' => '2024-04-12',
            'time' => '11:00:00',
            'location' => 'Mvog-Betsi Zoo',
            'address' => 'Mvog-Betsi, Yaoundé, Cameroon',
            'latitude' => 3.8333,
            'longitude' => 11.5000,
            'price' => 2000,
            'capacity' => 1000,
            'available_tickets' => 800,
            'image' => 'assets/images/events/food-festival.jpg',
            'category' => 'Culture',
            'status' => 'active',
            'created_at' => '2024-02-10 13:10:00'
        ]
    ];
}

// Demo user data
function getDemoUser() {
    return (object)[
        'id' => 1,
        'first_name' => 'Demo',
        'last_name' => 'User',
        'email' => '<EMAIL>',
        'phone' => '+237 6XX XXX XXX',
        'role' => 'user',
        'created_at' => '2024-01-01 00:00:00'
    ];
}

// Demo admin user
function getDemoAdmin() {
    return (object)[
        'id' => 2,
        'first_name' => 'Admin',
        'last_name' => 'User',
        'email' => '<EMAIL>',
        'phone' => '+237 6XX XXX XXX',
        'role' => 'admin',
        'created_at' => '2024-01-01 00:00:00'
    ];
}

// Demo bookings
function getDemoBookings() {
    return [
        (object)[
            'id' => 1,
            'user_id' => 1,
            'event_id' => 1,
            'booking_reference' => 'BK20240301ABC123',
            'quantity' => 2,
            'total_amount' => 50000,
            'status' => 'confirmed',
            'payment_status' => 'completed',
            'booking_date' => '2024-02-15 10:30:00',
            'qr_code' => 'demo-qr-code-1.png'
        ],
        (object)[
            'id' => 2,
            'user_id' => 1,
            'event_id' => 3,
            'booking_reference' => 'BK20240302DEF456',
            'quantity' => 1,
            'total_amount' => 8000,
            'status' => 'confirmed',
            'payment_status' => 'completed',
            'booking_date' => '2024-02-20 14:15:00',
            'qr_code' => 'demo-qr-code-2.png'
        ]
    ];
}

// Demo categories
function getDemoCategories() {
    return [
        'Technology',
        'Music',
        'Business',
        'Arts',
        'Culture',
        'Sports',
        'Education',
        'Health'
    ];
}

// Helper function to get events by category
function getDemoEventsByCategory($category = null) {
    $events = getDemoEvents();
    if ($category) {
        return array_filter($events, function($event) use ($category) {
            return $event->category === $category;
        });
    }
    return $events;
}

// Helper function to get event by ID
function getDemoEventById($id) {
    $events = getDemoEvents();
    foreach ($events as $event) {
        if ($event->id == $id) {
            return $event;
        }
    }
    return null;
}

// Helper function to search events
function searchDemoEvents($query) {
    $events = getDemoEvents();
    $query = strtolower($query);
    
    return array_filter($events, function($event) use ($query) {
        return strpos(strtolower($event->title), $query) !== false ||
               strpos(strtolower($event->description), $query) !== false ||
               strpos(strtolower($event->category), $query) !== false ||
               strpos(strtolower($event->location), $query) !== false;
    });
}

// Demo statistics for admin dashboard
function getDemoStats() {
    return (object)[
        'total_events' => 6,
        'total_bookings' => 2,
        'total_users' => 1,
        'total_revenue' => 58000,
        'active_events' => 6,
        'upcoming_events' => 6,
        'this_month_bookings' => 2,
        'this_month_revenue' => 58000
    ];
}
?>
