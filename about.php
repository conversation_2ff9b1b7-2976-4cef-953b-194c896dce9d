<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

$pageTitle = 'About Us - Meet the Developer';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="assets/css/modern-ui.css" rel="stylesheet">

    <!-- Leaflet CSS for OpenStreetMap -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin=""/>

    <style>
        .developer-section {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 100px 0;
        }

        .developer-photo {
            width: 300px;
            height: 300px;
            border-radius: 50%;
            object-fit: cover;
            border: 8px solid rgba(255,255,255,0.2);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            transition: transform 0.3s ease;
        }

        .developer-photo:hover {
            transform: scale(1.05);
        }

        .about-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-top: -100px;
            position: relative;
            z-index: 2;
        }

        .tech-stack {
            background: var(--light-bg);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
        }

        .tech-item {
            display: inline-block;
            background: var(--primary-color);
            color: white;
            padding: 8px 16px;
            border-radius: 25px;
            margin: 5px;
            font-size: 14px;
            font-weight: 500;
        }

        .university-info {
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            padding: 30px;
            border-left: 5px solid var(--primary-color);
        }

        .location-section {
            background: var(--light-bg);
            padding: 60px 0;
            margin-top: 50px;
        }

        .location-map {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        #developerLocationMap {
            height: 400px;
            width: 100%;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            background-color: #f8f9fa;
            border: 2px solid #dee2e6;
            position: relative;
            z-index: 1;
        }

        /* Ensure Leaflet controls are visible */
        .leaflet-control-container {
            z-index: 1000 !important;
        }

        /* Fix for potential CSS conflicts */
        #developerLocationMap .leaflet-container {
            height: 100% !important;
            width: 100% !important;
        }

        /* Ensure map container is properly displayed */
        .location-map {
            overflow: visible !important;
        }

        .col-lg-8 {
            overflow: visible !important;
        }

        /* Force map visibility */
        #developerLocationMap * {
            visibility: visible !important;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-calendar-star me-2"></i>
                ZARA-Events
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="events/">Browse Events</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="events/search.php">Search</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="about.php">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">Contact</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link position-relative" href="booking/cart.php">
                            <i class="fas fa-shopping-cart cart-icon"></i>
                            <span class="cart-count badge bg-danger position-absolute top-0 start-100 translate-middle" style="display: none;">0</span>
                        </a>
                    </li>
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                <?php echo htmlspecialchars($_SESSION['first_name'] ?? 'User'); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="user/dashboard.php">Dashboard</a></li>
                                <li><a class="dropdown-item" href="user/profile.php">Profile</a></li>
                                <?php if (isAdmin()): ?>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="admin/">Admin Panel</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="auth/logout.php">Logout</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="auth/login.php">Login</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-primary-modern ms-2" href="auth/register.php">Sign Up</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Developer Hero Section -->
    <section class="developer-section">
        <div class="container text-center">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <?php
                    $developerImage = 'assets/images/IMG_8931.jpg';
                    if (!file_exists($developerImage)) {
                        // Use a placeholder if the image doesn't exist
                        $developerImage = 'https://via.placeholder.com/300x300/007bff/ffffff?text=Tayong+Fritz';
                    }
                    ?>
                    <img src="<?php echo $developerImage; ?>" alt="Tayong Fritz - Developer" class="developer-photo mb-4">
                    <h1 class="display-4 fw-bold mb-3">Meet the Developer</h1>
                    <p class="lead">Passionate about creating innovative solutions for event management</p>
                </div>
            </div>
        </div>
    </section>

    <!-- About Content -->
    <section class="py-5">
        <div class="container">
            <div class="about-card">
                <div class="row">
                    <div class="col-lg-8 mx-auto text-center">
                        <h2 class="fw-bold mb-4">Tayong Fritz</h2>
                        <p class="lead text-muted mb-4">
                            Student at ICT University, Yaoundé | Full-Stack Developer | Event Technology Enthusiast
                        </p>

                        <div class="university-info mb-5">
                            <h4 class="fw-bold mb-3">
                                <i class="fas fa-graduation-cap me-2"></i>
                                Academic Background
                            </h4>
                            <p class="mb-2">
                                <strong>Institution:</strong> ICT University (Université des Technologies de l'Information et de la Communication)
                            </p>
                            <p class="mb-2">
                                <strong>Location:</strong> Yaoundé, Cameroon
                            </p>
                            <p class="mb-0">
                                <strong>Focus:</strong> Information and Communication Technology
                            </p>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <div class="card h-100 border-0 shadow-sm">
                                    <div class="card-body text-center">
                                        <i class="fas fa-lightbulb fa-3x text-primary mb-3"></i>
                                        <h5 class="fw-bold">Vision</h5>
                                        <p class="text-muted">To revolutionize event management in Central Africa through innovative technology solutions.</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-4">
                                <div class="card h-100 border-0 shadow-sm">
                                    <div class="card-body text-center">
                                        <i class="fas fa-heart fa-3x text-danger mb-3"></i>
                                        <h5 class="fw-bold">Passion</h5>
                                        <p class="text-muted">Creating user-friendly applications that solve real-world problems and enhance user experiences.</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="tech-stack">
                            <h4 class="fw-bold mb-3">
                                <i class="fas fa-code me-2"></i>
                                Technology Stack
                            </h4>
                            <div class="mb-3">
                                <span class="tech-item">PHP</span>
                                <span class="tech-item">MySQL</span>
                                <span class="tech-item">JavaScript</span>
                                <span class="tech-item">Bootstrap</span>
                                <span class="tech-item">HTML5</span>
                                <span class="tech-item">CSS3</span>
                                <span class="tech-item">jQuery</span>
                                <span class="tech-item">AJAX</span>
                                <span class="tech-item">Git</span>
                                <span class="tech-item">Docker</span>
                            </div>
                        </div>

                        <div class="row mt-5">
                            <div class="col-md-4 mb-3">
                                <div class="text-center">
                                    <i class="fas fa-calendar-check fa-2x text-primary mb-2"></i>
                                    <h6 class="fw-bold">Event Management</h6>
                                    <p class="text-muted small">Comprehensive booking systems</p>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="text-center">
                                    <i class="fas fa-mobile-alt fa-2x text-success mb-2"></i>
                                    <h6 class="fw-bold">Responsive Design</h6>
                                    <p class="text-muted small">Mobile-first approach</p>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="text-center">
                                    <i class="fas fa-shield-alt fa-2x text-warning mb-2"></i>
                                    <h6 class="fw-bold">Security</h6>
                                    <p class="text-muted small">Secure payment processing</p>
                                </div>
                            </div>
                        </div>

                        <div class="mt-5">
                            <h4 class="fw-bold mb-3">About ZARA-Events</h4>
                            <p class="text-muted">
                                ZARA-Events was developed as a comprehensive event booking platform specifically designed for the Central African market.
                                The system features modern UI/UX design, secure payment processing, real-time booking management, and administrative
                                tools that make event organization seamless and efficient.
                            </p>
                            <p class="text-muted">
                                Built with cutting-edge web technologies and following best practices in software development, this platform
                                demonstrates the potential of local talent in creating world-class applications.
                            </p>
                        </div>

                        <div class="mt-4">
                            <a href="contact.php" class="btn btn-primary-modern btn-lg me-3">
                                <i class="fas fa-envelope me-2"></i>Get In Touch
                            </a>
                            <a href="/" class="btn btn-outline-primary btn-lg">
                                <i class="fas fa-calendar me-2"></i>Explore Events
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Developer Location Section -->
    <section class="location-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center mb-5">
                    <h3 class="fw-bold">Based in Yaoundé</h3>
                    <p class="text-muted">Developing innovative solutions from the heart of Cameroon</p>
                </div>
            </div>

            <div class="location-map">
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <div id="developerLocationMap">
                            <!-- Loading indicator -->
                            <div id="mapLoadingIndicator" class="d-flex align-items-center justify-content-center h-100">
                                <div class="text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading map...</span>
                                    </div>
                                    <p class="mt-2 text-muted">Loading map...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="ps-lg-4">
                            <h5 class="fw-bold mb-3">
                                <i class="fas fa-graduation-cap me-2 text-primary"></i>
                                ICT University, Yaoundé
                            </h5>
                            <p class="text-muted mb-4">
                                Located in the vibrant capital city of Cameroon, ICT University provides
                                cutting-edge education in Information and Communication Technology.
                            </p>

                            <div class="mb-3">
                                <h6 class="fw-bold">
                                    <i class="fas fa-map-marker-alt me-2 text-success"></i>
                                    Location Highlights
                                </h6>
                                <ul class="list-unstyled text-muted">
                                    <li><i class="fas fa-university me-2 text-primary"></i>ICT University Campus</li>
                                    <li><i class="fas fa-city me-2 text-warning"></i>Yaoundé City Center</li>
                                    <li><i class="fas fa-flag me-2 text-success"></i>Capital of Cameroon</li>
                                    <li><i class="fas fa-globe-africa me-2 text-info"></i>Central Africa Hub</li>
                                </ul>
                            </div>

                            <div class="alert alert-primary">
                                <i class="fas fa-lightbulb me-2"></i>
                                <strong>Innovation Hub:</strong> Yaoundé is emerging as a major technology center in Central Africa.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-5" style="background: var(--dark-bg); color: white;">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-calendar-star me-2"></i>
                        ZARA-Events
                    </h5>
                    <p class="text-muted">Modern event booking platform for Central Africa. Discover, book, and experience amazing events with cutting-edge technology.</p>
                </div>

                <div class="col-lg-4 mb-4">
                    <h6 class="fw-bold mb-3">Quick Links</h6>
                    <ul class="list-unstyled">
                        <li><a href="/" class="text-muted text-decoration-none">Home</a></li>
                        <li><a href="about.php" class="text-muted text-decoration-none">About</a></li>
                        <li><a href="contact.php" class="text-muted text-decoration-none">Contact</a></li>
                        <li><a href="help-center.php" class="text-muted text-decoration-none">Help Center</a></li>
                    </ul>
                </div>

                <div class="col-lg-4 mb-4">
                    <h6 class="fw-bold mb-3">Legal</h6>
                    <ul class="list-unstyled">
                        <li><a href="privacy-policy.php" class="text-muted text-decoration-none">Privacy Policy</a></li>
                        <li><a href="terms-of-service.php" class="text-muted text-decoration-none">Terms of Service</a></li>
                    </ul>
                </div>
            </div>

            <hr class="my-4" style="border-color: rgba(255,255,255,0.1);">

            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-muted mb-0">&copy; 2024 ZARA-Events. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted mb-0">Developed by Tayong Fritz</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Leaflet JS for OpenStreetMap -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
            crossorigin=""></script>

    <!-- Custom JS -->
    <script src="assets/js/modern-app.js"></script>

    <!-- Simplified Map Initialization -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Wait for everything to load
            setTimeout(initializeAboutMap, 1000);
        });

        function initializeAboutMap() {
            console.log('Initializing about page map...');

            const mapContainer = document.getElementById('developerLocationMap');
            if (!mapContainer) {
                console.error('Map container not found');
                return;
            }

            if (typeof L === 'undefined') {
                console.error('Leaflet not loaded');
                return;
            }

            try {
                // Hide loading indicator
                const loadingIndicator = document.getElementById('mapLoadingIndicator');
                if (loadingIndicator) {
                    loadingIndicator.style.display = 'none';
                }

                // Clear container
                mapContainer.innerHTML = '';

                // Create map
                const map = L.map('developerLocationMap').setView([3.8480, 11.5021], 12);

                // Add tiles
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors',
                    maxZoom: 18
                }).addTo(map);

                // Add ICT University marker with custom popup
                L.marker([3.8480, 11.5021]).addTo(map)
                    .bindPopup(`
                        <div class="text-center p-2">
                            <strong><i class="fas fa-graduation-cap me-2 text-primary"></i>ICT University</strong><br>
                            <small class="text-muted">Yaoundé, Cameroon</small><br>
                            <small><strong>Developer Location</strong></small>
                        </div>
                    `)
                    .openPopup();

                // Add city center marker
                L.marker([3.8667, 11.5167]).addTo(map)
                    .bindPopup(`
                        <div class="text-center p-2">
                            <strong><i class="fas fa-building me-2 text-success"></i>Centre Ville</strong><br>
                            <small class="text-muted">City Center</small>
                        </div>
                    `);

                // Add service area circle
                L.circle([3.8480, 11.5021], {
                    color: '#007bff',
                    fillColor: '#007bff',
                    fillOpacity: 0.1,
                    radius: 8000,
                    weight: 2,
                    opacity: 0.6
                }).addTo(map).bindPopup(`
                    <div class="text-center p-2">
                        <strong><i class="fas fa-map-marked-alt me-2 text-info"></i>Service Area</strong><br>
                        <small class="text-muted">ZARA-Events Coverage Zone</small>
                    </div>
                `);

                console.log('Map created successfully');

                // Force resize
                setTimeout(() => map.invalidateSize(), 100);

            } catch (error) {
                console.error('Map error:', error);
            }
        }
    </script>


</body>
</html>
