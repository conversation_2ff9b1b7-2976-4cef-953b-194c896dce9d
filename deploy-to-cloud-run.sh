#!/bin/bash

# ZARA-Events Google Cloud Run Deployment Script
echo "🚀 ZARA-Events Google Cloud Run Deployment"
echo "==========================================="
echo ""

# Configuration
PROJECT_ID="${GOOGLE_CLOUD_PROJECT:-zara-ride}"  # Use environment variable or default
SERVICE_NAME="zara-events"
REGION="us-central1"
IMAGE="zaramillion/zara-events:latest"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}📋 Deployment Configuration:${NC}"
echo "   Project ID: $PROJECT_ID"
echo "   Service Name: $SERVICE_NAME"
echo "   Region: $REGION"
echo "   Image: $IMAGE"
echo ""

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo -e "${RED}❌ Google Cloud CLI is not installed.${NC}"
    echo "Please install it from: https://cloud.google.com/sdk/docs/install"
    exit 1
fi

# Check if user is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo -e "${YELLOW}⚠️  Not authenticated with Google Cloud.${NC}"
    echo "Running authentication..."
    gcloud auth login
fi

# Set project
echo -e "${BLUE}🔧 Setting up project...${NC}"
gcloud config set project $PROJECT_ID

# Enable required APIs
echo -e "${BLUE}🔌 Enabling required APIs...${NC}"
gcloud services enable run.googleapis.com
gcloud services enable sql-component.googleapis.com
gcloud services enable sqladmin.googleapis.com

# Deploy to Cloud Run
echo -e "${BLUE}🚀 Deploying to Cloud Run...${NC}"
gcloud run deploy $SERVICE_NAME \
    --image=$IMAGE \
    --platform=managed \
    --region=$REGION \
    --allow-unauthenticated \
    --port=80 \
    --memory=1Gi \
    --cpu=1 \
    --max-instances=10 \
    --set-env-vars="ENVIRONMENT=production" \
    --set-env-vars="SMTP_HOST=smtp.gmail.com" \
    --set-env-vars="SMTP_PORT=587" \
    --set-env-vars="SMTP_USERNAME=<EMAIL>" \
    --set-env-vars="SMTP_PASSWORD=pvjc rjit ogxg ncce" \
    --set-env-vars="FROM_EMAIL=<EMAIL>" \
    --set-env-vars="FROM_NAME=ZARA-Events" \
    --set-env-vars="ADMIN_EMAIL=<EMAIL>"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Deployment successful!${NC}"

    # Get the service URL
    SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region=$REGION --format="value(status.url)")

    echo ""
    echo -e "${GREEN}🎉 ZARA-Events is now live!${NC}"
    echo -e "${BLUE}🌐 Service URL: ${GREEN}$SERVICE_URL${NC}"
    echo ""
    echo -e "${BLUE}📱 Quick Access Links:${NC}"
    echo "   Main App:     $SERVICE_URL"
    echo "   About:        $SERVICE_URL/about.php"
    echo "   Contact:      $SERVICE_URL/contact.php"
    echo "   Help Center:  $SERVICE_URL/help-center.php"
    echo ""

    # Test the deployment
    echo -e "${BLUE}🧪 Testing deployment...${NC}"
    if curl -s -o /dev/null -w "%{http_code}" "$SERVICE_URL" | grep -q "200\|302"; then
        echo -e "${GREEN}✅ Service is responding correctly!${NC}"
    else
        echo -e "${YELLOW}⚠️  Service may still be starting up. Please check in a moment.${NC}"
    fi

else
    echo -e "${RED}❌ Deployment failed!${NC}"
    exit 1
fi

echo ""
echo -e "${BLUE}📊 Service Information:${NC}"
gcloud run services describe $SERVICE_NAME --region=$REGION --format="table(
    metadata.name,
    status.url,
    status.conditions[0].type,
    status.conditions[0].status
)"

echo ""
echo -e "${BLUE}🛠️  Management Commands:${NC}"
echo "   View logs:    gcloud run services logs read $SERVICE_NAME --region=$REGION"
echo "   Update:       gcloud run services update $SERVICE_NAME --region=$REGION"
echo "   Delete:       gcloud run services delete $SERVICE_NAME --region=$REGION"
echo ""

echo -e "${BLUE}💡 Next Steps:${NC}"
echo "1. Set up Cloud SQL database for production use"
echo "2. Configure custom domain (optional)"
echo "3. Set up monitoring and alerting"
echo "4. Configure CI/CD pipeline for automatic deployments"
echo ""

echo -e "${GREEN}🎯 Deployment Complete!${NC}"
