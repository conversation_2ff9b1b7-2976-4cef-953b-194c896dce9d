<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

echo "<h2>🧪 Registration Test Script</h2>";

// Test data for a new user
$testUserData = [
    'username' => 'testuser_' . time(), // Unique username
    'email' => 'test_' . time() . '@example.com', // Unique email
    'password' => 'testpassword123',
    'first_name' => 'Test',
    'last_name' => 'User',
    'phone' => '+237123456789',
    'address' => 'Test Address, Cameroon',
    'role' => 'user'
];

echo "<h3>📝 Test User Data:</h3>";
echo "<ul>";
foreach ($testUserData as $key => $value) {
    if ($key !== 'password') {
        echo "<li><strong>$key:</strong> $value</li>";
    } else {
        echo "<li><strong>$key:</strong> [hidden]</li>";
    }
}
echo "</ul>";

echo "<h3>🔍 Checking Database Connection:</h3>";
if (!$db->isConnected()) {
    echo "<p style='color: red;'>❌ Database not connected!</p>";
    exit;
}
echo "<p style='color: green;'>✅ Database connected successfully</p>";

echo "<h3>👥 Current Users in Database:</h3>";
$db->query('SELECT username, email, role FROM users');
$existingUsers = $db->resultset();
echo "<ul>";
foreach ($existingUsers as $user) {
    echo "<li>Username: <strong>{$user->username}</strong>, Email: <strong>{$user->email}</strong>, Role: {$user->role}</li>";
}
echo "</ul>";

echo "<h3>🧪 Testing Registration Process:</h3>";

// Step 1: Check if user already exists
echo "<h4>Step 1: Checking for existing user</h4>";
$db->query('SELECT id FROM users WHERE username = :username OR email = :email');
$db->bind(':username', $testUserData['username']);
$db->bind(':email', $testUserData['email']);
$existingUser = $db->single();

if ($existingUser) {
    echo "<p style='color: orange;'>⚠️ User already exists with this username or email</p>";
} else {
    echo "<p style='color: green;'>✅ Username and email are available</p>";
}

// Step 2: Test password hashing
echo "<h4>Step 2: Testing password hashing</h4>";
$hashedPassword = password_hash($testUserData['password'], PASSWORD_DEFAULT);
echo "<p>✅ Password hashed successfully: " . substr($hashedPassword, 0, 20) . "...</p>";

// Step 3: Test the registration function
echo "<h4>Step 3: Testing UserManager registration</h4>";
try {
    $result = $userManager->register($testUserData);
    if ($result) {
        echo "<p style='color: green;'>✅ Registration successful!</p>";
        
        // Verify the user was created
        $db->query('SELECT * FROM users WHERE username = :username');
        $db->bind(':username', $testUserData['username']);
        $newUser = $db->single();
        
        if ($newUser) {
            echo "<p style='color: green;'>✅ User verified in database:</p>";
            echo "<ul>";
            echo "<li>ID: {$newUser->id}</li>";
            echo "<li>Username: {$newUser->username}</li>";
            echo "<li>Email: {$newUser->email}</li>";
            echo "<li>Name: {$newUser->first_name} {$newUser->last_name}</li>";
            echo "<li>Role: {$newUser->role}</li>";
            echo "</ul>";
        }
    } else {
        echo "<p style='color: red;'>❌ Registration failed!</p>";
        
        // Check for specific error
        if ($existingUser) {
            echo "<p>Reason: Username or email already exists</p>";
        } else {
            echo "<p>Reason: Unknown database error</p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Registration error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h3>🔐 Testing Login Process:</h3>";

// Test login with existing user
echo "<h4>Testing login with admin user</h4>";
try {
    // First logout any existing session
    session_destroy();
    session_start();
    
    $loginResult = $userManager->login('admin', 'admin123');
    if ($loginResult) {
        echo "<p style='color: green;'>✅ Login successful for admin user!</p>";
        echo "<p>Session data:</p>";
        echo "<ul>";
        echo "<li>User ID: " . ($_SESSION['user_id'] ?? 'Not set') . "</li>";
        echo "<li>Username: " . ($_SESSION['username'] ?? 'Not set') . "</li>";
        echo "<li>Role: " . ($_SESSION['user_role'] ?? 'Not set') . "</li>";
        echo "<li>Name: " . ($_SESSION['user_name'] ?? 'Not set') . "</li>";
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>❌ Login failed for admin user!</p>";
        
        // Check if admin user exists and verify password
        $db->query('SELECT * FROM users WHERE username = :username');
        $db->bind(':username', 'admin');
        $adminUser = $db->single();
        
        if ($adminUser) {
            echo "<p>Admin user found in database</p>";
            $passwordCheck = password_verify('admin123', $adminUser->password);
            echo "<p>Password verification: " . ($passwordCheck ? "✅ Correct" : "❌ Incorrect") . "</p>";
        } else {
            echo "<p style='color: red;'>Admin user not found in database!</p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Login error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h3>📊 Summary:</h3>";
echo "<ul>";
echo "<li>Database Connection: " . ($db->isConnected() ? "✅ Working" : "❌ Failed") . "</li>";
echo "<li>User Registration: " . (isset($result) && $result ? "✅ Working" : "❌ Failed") . "</li>";
echo "<li>User Login: " . (isset($loginResult) && $loginResult ? "✅ Working" : "❌ Failed") . "</li>";
echo "<li>Developer Image: Fixed (IMG_8931.jpg)</li>";
echo "</ul>";

echo "<p><a href='auth/register.php'>Test Registration Form</a> | <a href='auth/login.php'>Test Login Form</a> | <a href='about.php'>Check About Page</a></p>";
?>
