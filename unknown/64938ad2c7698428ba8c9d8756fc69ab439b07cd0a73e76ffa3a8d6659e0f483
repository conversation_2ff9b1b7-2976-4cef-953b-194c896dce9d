<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Yaoundé Map Test - ZARA-Events</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" 
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" 
          crossorigin=""/>
    
    <style>
        .map-test-container {
            margin: 50px 0;
        }
        
        .test-map {
            height: 500px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .test-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-12 text-center my-5">
                <h1 class="display-4">
                    <i class="fas fa-map-marked-alt me-3 text-primary"></i>
                    Yaoundé Map Integration Test
                </h1>
                <p class="lead text-muted">Testing OpenStreetMap integration for ZARA-Events</p>
            </div>
        </div>
        
        <!-- Test Map 1: Full Service Area -->
        <div class="map-test-container">
            <div class="row">
                <div class="col-lg-8">
                    <h3><i class="fas fa-globe-africa me-2"></i>Service Area Map</h3>
                    <div id="serviceAreaMap" class="test-map"></div>
                </div>
                <div class="col-lg-4">
                    <div class="test-info">
                        <h5><i class="fas fa-info-circle me-2"></i>Test Details</h5>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Yaoundé Urban Community boundary</li>
                            <li><i class="fas fa-check text-success me-2"></i>Key landmarks</li>
                            <li><i class="fas fa-check text-success me-2"></i>Service area visualization</li>
                            <li><i class="fas fa-check text-success me-2"></i>Interactive popups</li>
                        </ul>
                        <div class="alert alert-info mt-3">
                            <small><strong>Data Source:</strong> OpenStreetMap Relation 2746229</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Map 2: Developer Location -->
        <div class="map-test-container">
            <div class="row">
                <div class="col-lg-8">
                    <h3><i class="fas fa-graduation-cap me-2"></i>Developer Location Map</h3>
                    <div id="developerMap" class="test-map"></div>
                </div>
                <div class="col-lg-4">
                    <div class="test-info">
                        <h5><i class="fas fa-university me-2"></i>ICT University Area</h5>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-map-marker-alt text-primary me-2"></i>ICT University location</li>
                            <li><i class="fas fa-building text-warning me-2"></i>Academic district</li>
                            <li><i class="fas fa-city text-success me-2"></i>City landmarks</li>
                            <li><i class="fas fa-flag text-info me-2"></i>Government quarter</li>
                        </ul>
                        <div class="alert alert-success mt-3">
                            <small><strong>Focus:</strong> University and surrounding areas</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Map 3: Event Location -->
        <div class="map-test-container">
            <div class="row">
                <div class="col-lg-8">
                    <h3><i class="fas fa-calendar-star me-2"></i>Event Location Map</h3>
                    <div id="eventMap" class="test-map"></div>
                </div>
                <div class="col-lg-4">
                    <div class="test-info">
                        <h5><i class="fas fa-map-pin me-2"></i>Event Venue Focus</h5>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-star text-warning me-2"></i>Event venue marker</li>
                            <li><i class="fas fa-circle text-primary me-2"></i>Venue area highlight</li>
                            <li><i class="fas fa-info text-info me-2"></i>Location details</li>
                            <li><i class="fas fa-route text-success me-2"></i>Accessibility info</li>
                        </ul>
                        <div class="alert alert-warning mt-3">
                            <small><strong>Purpose:</strong> Help users find event locations</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- API Status -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-server me-2"></i>API Status & Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6>OpenStreetMap</h6>
                                <p class="text-muted">Base map tiles and data</p>
                                <span class="badge bg-success">Active</span>
                            </div>
                            <div class="col-md-4">
                                <h6>Overpass API</h6>
                                <p class="text-muted">Boundary data queries</p>
                                <span class="badge bg-success" id="overpassStatus">Testing...</span>
                            </div>
                            <div class="col-md-4">
                                <h6>Leaflet.js</h6>
                                <p class="text-muted">Interactive map library</p>
                                <span class="badge bg-success">Loaded</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Navigation -->
        <div class="row mt-5 mb-5">
            <div class="col-12 text-center">
                <h5>Navigate to Implementation Pages</h5>
                <div class="btn-group" role="group">
                    <a href="contact.php" class="btn btn-primary">
                        <i class="fas fa-envelope me-2"></i>Contact Page
                    </a>
                    <a href="about.php" class="btn btn-success">
                        <i class="fas fa-user me-2"></i>About Page
                    </a>
                    <a href="events/" class="btn btn-warning">
                        <i class="fas fa-calendar me-2"></i>Events Page
                    </a>
                    <a href="/" class="btn btn-info">
                        <i class="fas fa-home me-2"></i>Home Page
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" 
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" 
            crossorigin=""></script>
    
    <!-- Yaoundé Map Integration -->
    <script src="assets/js/yaounde-map.js"></script>
    
    <!-- Test Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const yaoundeMapInstance = new YaoundeMap();
            
            // Test 1: Service Area Map
            yaoundeMapInstance.initializeMap('serviceAreaMap', {
                showBoundary: true,
                showLandmarks: true,
                showServiceArea: true,
                zoom: 11
            }).then(() => {
                console.log('Service area map initialized successfully');
            }).catch(error => {
                console.error('Service area map failed:', error);
            });
            
            // Test 2: Developer Location Map
            yaoundeMapInstance.initializeMap('developerMap', {
                center: [3.8480, 11.5021],
                zoom: 12,
                showBoundary: false,
                showLandmarks: true,
                showServiceArea: true
            }).then(() => {
                console.log('Developer location map initialized successfully');
            }).catch(error => {
                console.error('Developer location map failed:', error);
            });
            
            // Test 3: Event Location Map
            yaoundeMapInstance.initializeMap('eventMap', {
                center: [3.8480, 11.5021],
                zoom: 13,
                showBoundary: false,
                showLandmarks: false,
                showServiceArea: false
            }).then(map => {
                // Add a sample event marker
                L.marker([3.8480, 11.5021])
                    .addTo(map)
                    .bindPopup(`
                        <div class="text-center">
                            <strong><i class="fas fa-calendar-star me-2"></i>Sample Event</strong><br>
                            <small>Conference Center Yaoundé</small><br>
                            <small class="text-muted">Centre Ville, Yaoundé</small>
                        </div>
                    `)
                    .openPopup();
                    
                console.log('Event location map initialized successfully');
            }).catch(error => {
                console.error('Event location map failed:', error);
            });
            
            // Test Overpass API connectivity
            testOverpassAPI();
        });
        
        async function testOverpassAPI() {
            const statusElement = document.getElementById('overpassStatus');
            try {
                const response = await fetch('https://overpass-api.de/api/interpreter', {
                    method: 'POST',
                    body: '[out:json][timeout:5];(relation(2746229););out;',
                    headers: { 'Content-Type': 'text/plain' }
                });
                
                if (response.ok) {
                    statusElement.textContent = 'Active';
                    statusElement.className = 'badge bg-success';
                } else {
                    statusElement.textContent = 'Limited';
                    statusElement.className = 'badge bg-warning';
                }
            } catch (error) {
                statusElement.textContent = 'Fallback';
                statusElement.className = 'badge bg-secondary';
            }
        }
    </script>
</body>
</html>
