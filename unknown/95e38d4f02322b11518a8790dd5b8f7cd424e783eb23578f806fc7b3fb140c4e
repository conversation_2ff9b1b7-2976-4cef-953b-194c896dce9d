#!/bin/bash

# Simple MySQL setup script for ZARA-Events
echo "Setting up MySQL for ZARA-Events..."

# Stop MySQL service
echo "Stopping MySQL service..."
brew services stop mysql

# Start MySQL in safe mode
echo "Starting MySQL in safe mode..."
sudo mysqld_safe --skip-grant-tables --skip-networking &
MYSQL_PID=$!

# Wait for MySQL to start
sleep 10

# Connect and set up database
echo "Setting up database and user..."
mysql -u root << EOF
FLUSH PRIVILEGES;
ALTER USER 'root'@'localhost' IDENTIFIED BY '';
CREATE DATABASE IF NOT EXISTS event_booking_system;
CREATE USER IF NOT EXISTS 'event_user'@'localhost' IDENTIFIED BY 'event_password';
GRANT ALL PRIVILEGES ON event_booking_system.* TO 'event_user'@'localhost';
FLUSH PRIVILEGES;
EOF

# Kill the safe mode MySQL
sudo kill $MYSQL_PID

# Start MySQL normally
echo "Starting MySQL normally..."
brew services start mysql

echo "MySQL setup complete!"
echo "Database: event_booking_system"
echo "User: event_user"
echo "Password: event_password"
