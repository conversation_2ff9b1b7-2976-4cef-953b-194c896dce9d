FROM php:8.1-apache

# Install system dependencies
RUN apt-get update && apt-get install -y \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    libzip-dev \
    unzip \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) gd pdo pdo_mysql zip

# Enable Apache modules
RUN a2enmod rewrite headers

# Set working directory
WORKDIR /var/www/html

# Copy application files
COPY . .

# Set proper permissions
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html

# Configure Apache for Cloud Run
RUN echo 'ServerName localhost' >> /etc/apache2/apache2.conf

# Configure Apache for PWA
RUN echo '<Directory /var/www/html>' >> /etc/apache2/apache2.conf \
    && echo '    Options Indexes FollowSymLinks' >> /etc/apache2/apache2.conf \
    && echo '    AllowOverride All' >> /etc/apache2/apache2.conf \
    && echo '    Require all granted' >> /etc/apache2/apache2.conf \
    && echo '</Directory>' >> /etc/apache2/apache2.conf

# Add PWA-specific headers
RUN echo 'Header always set X-Content-Type-Options nosniff' >> /etc/apache2/apache2.conf \
    && echo 'Header always set X-Frame-Options DENY' >> /etc/apache2/apache2.conf \
    && echo 'Header always set X-XSS-Protection "1; mode=block"' >> /etc/apache2/apache2.conf

# Create a simple startup script for Cloud Run
RUN echo '#!/bin/bash\n\
set -e\n\
\n\
# Use PORT environment variable provided by Cloud Run\n\
PORT=${PORT:-8080}\n\
echo "Configuring Apache to listen on port $PORT"\n\
\n\
# Update ports.conf\n\
echo "Listen $PORT" > /etc/apache2/ports.conf\n\
\n\
# Update default site configuration\n\
echo "<VirtualHost *:$PORT>" > /etc/apache2/sites-available/000-default.conf\n\
echo "    DocumentRoot /var/www/html" >> /etc/apache2/sites-available/000-default.conf\n\
echo "    ErrorLog ${APACHE_LOG_DIR}/error.log" >> /etc/apache2/sites-available/000-default.conf\n\
echo "    CustomLog ${APACHE_LOG_DIR}/access.log combined" >> /etc/apache2/sites-available/000-default.conf\n\
echo "</VirtualHost>" >> /etc/apache2/sites-available/000-default.conf\n\
\n\
echo "Starting Apache on port $PORT"\n\
exec apache2-foreground' > /usr/local/bin/docker-entrypoint.sh

RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# Expose port 8080 (Cloud Run will map this to the PORT env var)
EXPOSE 8080

# Use the custom entrypoint
CMD ["/usr/local/bin/docker-entrypoint.sh"]
