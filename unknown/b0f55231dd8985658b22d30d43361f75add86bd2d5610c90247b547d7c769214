<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Get event ID
$eventId = $_GET['id'] ?? 0;

if (!$eventId) {
    redirect('./');
}

// Get event details
$event = $eventManager->getEventById($eventId);

if (!$event) {
    redirect('./');
}

$pageTitle = $event->title;

// Get related events (same category)
$relatedEvents = $eventManager->getEventsByCategory($event->category);
$relatedEvents = array_filter($relatedEvents, function($e) use ($eventId) {
    return $e->id != $eventId;
});
$relatedEvents = array_slice($relatedEvents, 0, 3);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?> - <?php echo SITE_NAME; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="../assets/css/modern-ui.css" rel="stylesheet">

    <!-- Leaflet CSS for OpenStreetMap -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin=""/>

    <!-- Meta tags for social sharing -->
    <meta property="og:title" content="<?php echo htmlspecialchars($event->title); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars(substr($event->description, 0, 160)); ?>">
    <meta property="og:image" content="<?php echo $event->image_url; ?>">
    <meta property="og:type" content="event">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container">
            <a class="navbar-brand" href="../">
                <i class="fas fa-calendar-star me-2"></i>
                EventHub
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="./">Browse Events</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="search.php">Search</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link position-relative" href="../booking/cart.php">
                            <i class="fas fa-shopping-cart cart-icon"></i>
                            <span class="cart-count badge bg-danger position-absolute top-0 start-100 translate-middle" style="display: none;">0</span>
                        </a>
                    </li>
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                <?php echo htmlspecialchars($_SESSION['first_name'] ?? 'User'); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="../user/dashboard.php">Dashboard</a></li>
                                <li><a class="dropdown-item" href="../user/profile.php">Profile</a></li>
                                <?php if (isAdmin()): ?>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="../admin/">Admin Panel</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../auth/logout.php">Logout</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="../auth/login.php">Login</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-primary-modern ms-2" href="../auth/register.php">Sign Up</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../">Home</a></li>
                    <li class="breadcrumb-item"><a href="./">Events</a></li>
                    <li class="breadcrumb-item active"><?php echo htmlspecialchars($event->title); ?></li>
                </ol>
            </nav>

            <div class="row">
                <!-- Event Details -->
                <div class="col-lg-8 mb-4">
                    <div class="card-modern">
                        <!-- Event Image -->
                        <div class="event-hero-image">
                            <img src="<?php echo $event->image_url ?: 'https://images.unsplash.com/photo-1492684223066-81342ee5ff30?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'; ?>"
                                 alt="<?php echo htmlspecialchars($event->title); ?>"
                                 class="img-fluid w-100" style="height: 400px; object-fit: cover;">

                            <div class="position-absolute top-0 start-0 m-3">
                                <span class="badge bg-primary fs-6">
                                    <?php echo htmlspecialchars($event->category); ?>
                                </span>
                            </div>

                            <?php if ($event->available_tickets < 10): ?>
                                <div class="position-absolute top-0 end-0 m-3">
                                    <span class="badge bg-warning text-dark fs-6">
                                        <i class="fas fa-fire me-1"></i>Almost Sold Out
                                    </span>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="card-body">
                            <h1 class="mb-3"><?php echo htmlspecialchars($event->title); ?></h1>

                            <!-- Event Meta Info -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-3">
                                        <i class="fas fa-calendar-alt text-primary me-3 fa-lg"></i>
                                        <div>
                                            <strong>Date & Time</strong><br>
                                            <span class="text-muted">
                                                <?php echo formatDate($event->event_date); ?> at <?php echo formatTime($event->event_time); ?>
                                            </span>
                                        </div>
                                    </div>

                                    <div class="d-flex align-items-center mb-3">
                                        <i class="fas fa-map-marker-alt text-primary me-3 fa-lg"></i>
                                        <div>
                                            <strong>Location</strong><br>
                                            <span class="text-muted">
                                                <?php echo htmlspecialchars($event->venue); ?><br>
                                                <?php echo htmlspecialchars($event->location); ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-3">
                                        <i class="fas fa-user text-primary me-3 fa-lg"></i>
                                        <div>
                                            <strong>Organizer</strong><br>
                                            <span class="text-muted">
                                                <?php echo htmlspecialchars($event->organizer); ?><br>
                                                <?php echo htmlspecialchars($event->organizer_contact); ?>
                                            </span>
                                        </div>
                                    </div>

                                    <div class="d-flex align-items-center mb-3">
                                        <i class="fas fa-ticket-alt text-primary me-3 fa-lg"></i>
                                        <div>
                                            <strong>Availability</strong><br>
                                            <span class="text-muted">
                                                <?php echo $event->available_tickets; ?> of <?php echo $event->total_tickets; ?> tickets left
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Event Description -->
                            <div class="mb-4">
                                <h4>About This Event</h4>
                                <div class="event-description">
                                    <?php echo nl2br(htmlspecialchars($event->description)); ?>
                                </div>
                            </div>

                            <!-- Event Location Map -->
                            <div class="mb-4">
                                <h4>Event Location</h4>
                                <div class="row">
                                    <div class="col-lg-8">
                                        <div id="eventLocationMap" style="height: 300px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);"></div>
                                    </div>
                                    <div class="col-lg-4">
                                        <div class="ps-lg-3">
                                            <div class="card border-0 bg-light h-100">
                                                <div class="card-body">
                                                    <h6 class="fw-bold mb-3">
                                                        <i class="fas fa-map-marker-alt me-2 text-primary"></i>
                                                        Venue Details
                                                    </h6>
                                                    <p class="mb-2">
                                                        <strong>Venue:</strong><br>
                                                        <?php echo htmlspecialchars($event->venue); ?>
                                                    </p>
                                                    <p class="mb-3">
                                                        <strong>Address:</strong><br>
                                                        <?php echo htmlspecialchars($event->location); ?>
                                                    </p>
                                                    <div class="alert alert-info alert-sm">
                                                        <i class="fas fa-info-circle me-2"></i>
                                                        <small>Located within Yaoundé Urban Community</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Social Sharing -->
                            <div class="mb-4">
                                <h5>Share This Event</h5>
                                <div class="social-share">
                                    <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(SITE_URL . '/events/details.php?id=' . $event->id); ?>"
                                       target="_blank" class="btn btn-outline-primary btn-sm me-2">
                                        <i class="fab fa-facebook-f me-1"></i>Facebook
                                    </a>
                                    <a href="https://twitter.com/intent/tweet?text=<?php echo urlencode($event->title); ?>&url=<?php echo urlencode(SITE_URL . '/events/details.php?id=' . $event->id); ?>"
                                       target="_blank" class="btn btn-outline-info btn-sm me-2">
                                        <i class="fab fa-twitter me-1"></i>Twitter
                                    </a>
                                    <a href="https://wa.me/?text=<?php echo urlencode($event->title . ' - ' . SITE_URL . '/events/details.php?id=' . $event->id); ?>"
                                       target="_blank" class="btn btn-outline-success btn-sm">
                                        <i class="fab fa-whatsapp me-1"></i>WhatsApp
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Booking Sidebar -->
                <div class="col-lg-4">
                    <div class="card-modern sticky-top" style="top: 100px;">
                        <div class="card-body">
                            <div class="text-center mb-4">
                                <h3 class="text-primary mb-0"><?php echo formatCurrency($event->price); ?></h3>
                                <small class="text-muted">per ticket</small>
                            </div>

                            <?php if ($event->available_tickets > 0): ?>
                                <div class="booking-form" data-event-id="<?php echo $event->id; ?>">
                                    <div class="mb-3">
                                        <label for="quantity" class="form-label">Number of Tickets</label>
                                        <select class="form-control form-control-modern" id="quantity" name="quantity">
                                            <?php for ($i = 1; $i <= min(10, $event->available_tickets); $i++): ?>
                                                <option value="<?php echo $i; ?>"><?php echo $i; ?> ticket<?php echo $i > 1 ? 's' : ''; ?></option>
                                            <?php endfor; ?>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between">
                                            <span>Subtotal:</span>
                                            <span class="fw-bold subtotal"><?php echo formatCurrency($event->price); ?></span>
                                        </div>
                                    </div>

                                    <?php if (isLoggedIn()): ?>
                                        <div class="d-grid gap-2">
                                            <button class="btn btn-primary-modern btn-lg add-to-cart-btn">
                                                <i class="fas fa-cart-plus me-2"></i>
                                                Add to Cart
                                            </button>
                                            <a href="../booking/checkout.php?event_id=<?php echo $event->id; ?>&quantity=1"
                                               class="btn btn-outline-primary quick-book-btn">
                                                <i class="fas fa-bolt me-2"></i>
                                                Quick Book
                                            </a>
                                        </div>
                                    <?php else: ?>
                                        <div class="d-grid">
                                            <a href="../auth/login.php?redirect=<?php echo urlencode($_SERVER['REQUEST_URI']); ?>"
                                               class="btn btn-primary-modern btn-lg">
                                                <i class="fas fa-sign-in-alt me-2"></i>
                                                Login to Book
                                            </a>
                                        </div>
                                        <p class="text-center text-muted mt-2 small">
                                            Don't have an account?
                                            <a href="../auth/register.php">Sign up here</a>
                                        </p>
                                    <?php endif; ?>
                                </div>
                            <?php else: ?>
                                <div class="text-center">
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        This event is sold out
                                    </div>
                                    <button class="btn btn-outline-secondary" disabled>
                                        <i class="fas fa-times me-2"></i>
                                        Sold Out
                                    </button>
                                </div>
                            <?php endif; ?>

                            <!-- Event Stats -->
                            <div class="mt-4 pt-4 border-top">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="stat-item">
                                            <h5 class="mb-0"><?php echo $event->total_tickets - $event->available_tickets; ?></h5>
                                            <small class="text-muted">Tickets Sold</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="stat-item">
                                            <h5 class="mb-0"><?php echo $event->available_tickets; ?></h5>
                                            <small class="text-muted">Available</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Related Events -->
            <?php if (!empty($relatedEvents)): ?>
                <div class="row mt-5">
                    <div class="col-12">
                        <h3 class="mb-4">Related Events</h3>
                    </div>
                    <?php foreach ($relatedEvents as $relatedEvent): ?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="event-card" data-event-id="<?php echo $relatedEvent->id; ?>">
                                <div class="event-image-container">
                                    <img src="<?php echo $relatedEvent->image_url ?: 'https://images.unsplash.com/photo-1492684223066-81342ee5ff30?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'; ?>"
                                         alt="<?php echo htmlspecialchars($relatedEvent->title); ?>"
                                         class="event-image">
                                </div>
                                <div class="event-content">
                                    <h6 class="event-title"><?php echo htmlspecialchars($relatedEvent->title); ?></h6>
                                    <div class="event-meta mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            <?php echo formatDate($relatedEvent->event_date); ?>
                                        </small>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="fw-bold"><?php echo formatCurrency($relatedEvent->price); ?></span>
                                        <a href="details.php?id=<?php echo $relatedEvent->id; ?>"
                                           class="btn btn-outline-primary btn-sm">
                                            View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Leaflet JS for OpenStreetMap -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
            crossorigin=""></script>

    <!-- Custom JS -->
    <script src="../assets/js/modern-app.js"></script>

    <!-- Event Details JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadCartCount();

            // Update subtotal when quantity changes
            const quantitySelect = document.getElementById('quantity');
            const subtotalElement = document.querySelector('.subtotal');
            const pricePerTicket = <?php echo $event->price; ?>;

            if (quantitySelect && subtotalElement) {
                quantitySelect.addEventListener('change', function() {
                    const quantity = parseInt(this.value);
                    const subtotal = quantity * pricePerTicket;
                    subtotalElement.textContent = new Intl.NumberFormat('fr-FR').format(subtotal) + ' <?php echo CURRENCY_SYMBOL; ?>';

                    // Update quick book link
                    const quickBookBtn = document.querySelector('.quick-book-btn');
                    if (quickBookBtn) {
                        const url = new URL(quickBookBtn.href);
                        url.searchParams.set('quantity', quantity);
                        quickBookBtn.href = url.toString();
                    }
                });
            }

            // Initialize event location map
            if (document.getElementById('eventLocationMap')) {
                // Create a simple map for the event location
                const eventMap = L.map('eventLocationMap').setView([3.8480, 11.5021], 13);

                // Add OpenStreetMap tiles
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                    maxZoom: 18
                }).addTo(eventMap);

                // Add marker for event location (using approximate coordinates)
                const eventMarker = L.marker([3.8480, 11.5021])
                    .addTo(eventMap)
                    .bindPopup(`
                        <div class="text-center">
                            <strong><i class="fas fa-calendar-star me-2"></i><?php echo htmlspecialchars($event->title); ?></strong><br>
                            <small><?php echo htmlspecialchars($event->venue); ?></small><br>
                            <small class="text-muted"><?php echo htmlspecialchars($event->location); ?></small>
                        </div>
                    `)
                    .openPopup();

                // Add a circle to show the general area
                L.circle([3.8480, 11.5021], {
                    color: '#007bff',
                    fillColor: '#007bff',
                    fillOpacity: 0.1,
                    radius: 2000
                }).addTo(eventMap);
            }
        });
    </script>
</body>
</html>
