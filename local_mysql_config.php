<?php
// Configuration for connecting deployed app to local MySQL
// Use this configuration in your deployed application

// Database configuration for local MySQL
define('DB_HOST', 'YOUR_LOCAL_IP_ADDRESS'); // Replace with your actual local IP
define('DB_USER', 'event_user');
define('DB_PASS', 'event_password');
define('DB_NAME', 'event_booking_system');
define('DB_PORT', '3306');

// Example configurations for different scenarios:

// Option 1: If your deployed app can access your local machine directly
// You'll need to replace 'YOUR_LOCAL_IP_ADDRESS' with your actual local IP
// Find your local IP with: ifconfig | grep "inet " | grep -v 127.0.0.1

// Option 2: If using a tunnel service like ngrok
// define('DB_HOST', 'your-ngrok-url.ngrok.io');
// define('DB_PORT', 'ngrok-port');

// Option 3: If using a cloud database service
// You might want to export your local database and import it to a cloud service

echo "Local MySQL Configuration:\n";
echo "==========================\n";
echo "Host: " . DB_HOST . "\n";
echo "Port: " . DB_PORT . "\n";
echo "Database: " . DB_NAME . "\n";
echo "Username: " . DB_USER . "\n";
echo "Password: " . DB_PASS . "\n";
echo "\n";
echo "Connection String: mysql://" . DB_USER . ":" . DB_PASS . "@" . DB_HOST . ":" . DB_PORT . "/" . DB_NAME . "\n";
?>
