#!/usr/bin/env python3
"""
PDF Generator for ZARA-Events User Manual
Converts the Markdown user manual to a professional HTML document that can be printed to PDF
"""

import markdown
import os
import sys
from datetime import datetime

def generate_html_from_markdown():
    """Convert the user manual markdown to a professional HTML document"""

    # File paths
    markdown_file = 'docs/04-USER-MANUAL.md'
    output_html = 'docs/ZARA-Events-User-Manual.html'

    # Check if markdown file exists
    if not os.path.exists(markdown_file):
        print(f"Error: {markdown_file} not found!")
        return False
    
    try:
        # Read the markdown content
        with open(markdown_file, 'r', encoding='utf-8') as f:
            markdown_content = f.read()
        
        # Convert markdown to HTML
        md = markdown.Markdown(extensions=['tables', 'toc', 'codehilite'])
        html_content = md.convert(markdown_content)
        
        # Create a complete HTML document with styling
        html_document = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>ZARA-Events User Manual</title>
            <style>
                body {{
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 800px;
                    margin: 0 auto;
                    padding: 20px;
                    background-color: #fff;
                }}
                
                h1 {{
                    color: #408681;
                    border-bottom: 3px solid #408681;
                    padding-bottom: 10px;
                    font-size: 2.5em;
                    margin-top: 30px;
                }}
                
                h2 {{
                    color: #408681;
                    border-bottom: 2px solid #FBF1DF;
                    padding-bottom: 8px;
                    font-size: 1.8em;
                    margin-top: 25px;
                }}
                
                h3 {{
                    color: #2c5f5a;
                    font-size: 1.3em;
                    margin-top: 20px;
                }}
                
                h4 {{
                    color: #2c5f5a;
                    font-size: 1.1em;
                    margin-top: 15px;
                }}
                
                p {{
                    margin-bottom: 12px;
                    text-align: justify;
                }}
                
                ul, ol {{
                    margin-bottom: 15px;
                    padding-left: 25px;
                }}
                
                li {{
                    margin-bottom: 5px;
                }}
                
                strong {{
                    color: #408681;
                    font-weight: 600;
                }}
                
                code {{
                    background-color: #f4f4f4;
                    padding: 2px 4px;
                    border-radius: 3px;
                    font-family: 'Courier New', monospace;
                    font-size: 0.9em;
                }}
                
                pre {{
                    background-color: #f8f8f8;
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    padding: 15px;
                    overflow-x: auto;
                    margin: 15px 0;
                }}
                
                blockquote {{
                    border-left: 4px solid #408681;
                    margin: 15px 0;
                    padding-left: 15px;
                    color: #666;
                    font-style: italic;
                }}
                
                table {{
                    border-collapse: collapse;
                    width: 100%;
                    margin: 15px 0;
                }}
                
                th, td {{
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: left;
                }}
                
                th {{
                    background-color: #408681;
                    color: white;
                    font-weight: bold;
                }}
                
                tr:nth-child(even) {{
                    background-color: #f9f9f9;
                }}
                
                .header {{
                    text-align: center;
                    margin-bottom: 30px;
                    padding: 20px;
                    background: linear-gradient(135deg, #408681, #FBF1DF);
                    border-radius: 10px;
                    color: white;
                }}
                
                .footer {{
                    text-align: center;
                    margin-top: 30px;
                    padding: 15px;
                    border-top: 2px solid #408681;
                    color: #666;
                    font-size: 0.9em;
                }}
                
                .page-break {{
                    page-break-before: always;
                }}
                
                @media print {{
                    body {{
                        font-size: 12pt;
                        line-height: 1.4;
                    }}
                    
                    h1 {{
                        font-size: 18pt;
                    }}
                    
                    h2 {{
                        font-size: 16pt;
                    }}
                    
                    h3 {{
                        font-size: 14pt;
                    }}
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1 style="margin: 0; border: none; color: white;">📅 ZARA-Events</h1>
                <h2 style="margin: 10px 0 0 0; border: none; color: #FBF1DF;">Complete User Manual</h2>
                <p style="margin: 10px 0 0 0; font-size: 1.1em;">
                    Your Complete Guide to Event Discovery and Booking
                </p>
                <p style="margin: 5px 0 0 0; font-size: 0.9em;">
                    Generated on {datetime.now().strftime('%B %d, %Y')}
                </p>
            </div>
            
            {html_content}
            
            <div class="footer">
                <p><strong>ZARA-Events User Manual</strong></p>
                <p>Developed by Tayong Fritz Vugah | ICT University, Yaoundé, Cameroon</p>
                <p>Contact: <EMAIL> | WhatsApp: +237 651 408 682</p>
                <p>© {datetime.now().year} ZARA-Events. All rights reserved.</p>
            </div>
        </body>
        </html>
        """
        
        # Save the HTML file
        with open(output_html, 'w', encoding='utf-8') as f:
            f.write(html_document)

        print(f"✅ HTML successfully generated: {output_html}")
        print(f"📄 File size: {os.path.getsize(output_html) / 1024:.1f} KB")
        print("\n📋 To convert to PDF:")
        print("1. Open the HTML file in your browser")
        print("2. Press Ctrl+P (Cmd+P on Mac) to print")
        print("3. Select 'Save as PDF' as the destination")
        print("4. Choose appropriate settings and save")
        return True
        
    except ImportError as e:
        print("❌ Missing required packages. Installing...")
        print("Please run: pip install markdown")
        return False

    except Exception as e:
        print(f"❌ Error generating HTML: {str(e)}")
        return False

if __name__ == "__main__":
    success = generate_html_from_markdown()
    if success:
        print("\n🎉 HTML generation completed successfully!")
        print("📁 You can find the HTML file at: docs/ZARA-Events-User-Manual.html")
        print("📄 Open this file in your browser and print to PDF for your lecturer.")
    else:
        print("\n❌ HTML generation failed. Please check the error messages above.")
        sys.exit(1)
