<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - ZARA-Events</title>
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#408681">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="ZARA-Events">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #408681, #FBF1DF);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .offline-container {
            text-align: center;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 3rem 2rem;
            box-shadow: 0 20px 60px rgba(64, 134, 129, 0.2);
            max-width: 500px;
            margin: 2rem;
        }
        
        .offline-icon {
            font-size: 4rem;
            color: #408681;
            margin-bottom: 1.5rem;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        h1 {
            color: #408681;
            font-size: 2rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }
        
        p {
            color: #666;
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
        }
        
        .retry-btn {
            background: linear-gradient(135deg, #408681, #2c5f5a);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
        }
        
        .retry-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(64, 134, 129, 0.3);
        }
        
        .offline-features {
            margin-top: 2rem;
            text-align: left;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            padding: 0.5rem;
            background: rgba(64, 134, 129, 0.1);
            border-radius: 10px;
        }
        
        .feature-icon {
            color: #408681;
            font-size: 1.2rem;
            margin-right: 1rem;
            width: 24px;
        }
        
        .connection-status {
            margin-top: 1.5rem;
            padding: 1rem;
            border-radius: 10px;
            font-weight: 600;
        }
        
        .status-offline {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
            border: 1px solid rgba(220, 53, 69, 0.2);
        }
        
        .status-online {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
            border: 1px solid rgba(40, 167, 69, 0.2);
        }
        
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
            margin-right: 0.5rem;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .offline-container {
                margin: 1rem;
                padding: 2rem 1.5rem;
            }
            
            h1 {
                font-size: 1.5rem;
            }
            
            .offline-icon {
                font-size: 3rem;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">📡</div>
        <h1>You're Offline</h1>
        <p>It looks like you've lost your internet connection. Don't worry, some features of ZARA-Events are still available offline!</p>
        
        <div class="offline-features">
            <div class="feature-item">
                <div class="feature-icon">📱</div>
                <div>Browse cached events and information</div>
            </div>
            <div class="feature-item">
                <div class="feature-icon">🎫</div>
                <div>View your downloaded tickets and QR codes</div>
            </div>
            <div class="feature-item">
                <div class="feature-icon">📋</div>
                <div>Access your booking history</div>
            </div>
            <div class="feature-item">
                <div class="feature-icon">⚡</div>
                <div>Automatic sync when connection returns</div>
            </div>
        </div>
        
        <div class="connection-status status-offline" id="connectionStatus">
            🔴 No Internet Connection
        </div>
        
        <button class="retry-btn" onclick="checkConnection()" id="retryBtn">
            Check Connection
        </button>
        
        <a href="/" class="retry-btn" style="background: linear-gradient(135deg, #6c757d, #495057);">
            Go to Home
        </a>
    </div>

    <script>
        // Check connection status
        function updateConnectionStatus() {
            const status = document.getElementById('connectionStatus');
            const retryBtn = document.getElementById('retryBtn');
            
            if (navigator.onLine) {
                status.className = 'connection-status status-online';
                status.innerHTML = '🟢 Connection Restored!';
                retryBtn.innerHTML = '<div class="loading-spinner"></div>Redirecting...';
                
                // Redirect to home page after a short delay
                setTimeout(() => {
                    window.location.href = '/';
                }, 2000);
            } else {
                status.className = 'connection-status status-offline';
                status.innerHTML = '🔴 No Internet Connection';
                retryBtn.innerHTML = 'Check Connection';
            }
        }
        
        function checkConnection() {
            const retryBtn = document.getElementById('retryBtn');
            retryBtn.innerHTML = '<div class="loading-spinner"></div>Checking...';
            
            // Simulate checking connection
            setTimeout(() => {
                updateConnectionStatus();
            }, 1000);
        }
        
        // Listen for online/offline events
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);
        
        // Initial status check
        updateConnectionStatus();
        
        // Periodic connection check
        setInterval(() => {
            if (navigator.onLine) {
                // Try to fetch a small resource to verify actual connectivity
                fetch('/manifest.json', { 
                    method: 'HEAD',
                    cache: 'no-cache'
                }).then(() => {
                    if (!document.querySelector('.status-online')) {
                        updateConnectionStatus();
                    }
                }).catch(() => {
                    // Still offline despite navigator.onLine being true
                });
            }
        }, 5000);
        
        // Register service worker if not already registered
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js').catch(error => {
                console.log('Service Worker registration failed:', error);
            });
        }
    </script>
</body>
</html>
