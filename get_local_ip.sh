#!/bin/bash

# <PERSON>ript to get your local IP address for database connection

echo "🔍 Finding your local IP addresses..."
echo "====================================="
echo ""

# Get local IP addresses
echo "Local IP addresses found:"
ifconfig | grep "inet " | grep -v 127.0.0.1 | awk '{print $2}'

echo ""
echo "📝 Instructions for connecting your deployed app to local MySQL:"
echo "================================================================"
echo ""
echo "1. Choose one of the IP addresses above (usually the one starting with 192.168.x.x)"
echo "2. Update your deployed application's database configuration to use:"
echo "   - Host: [YOUR_IP_ADDRESS]"
echo "   - Port: 3306"
echo "   - Database: event_booking_system"
echo "   - Username: event_user"
echo "   - Password: event_password"
echo ""
echo "3. Make sure your local MySQL allows remote connections:"
echo "   - Edit MySQL config: sudo nano /opt/homebrew/etc/my.cnf"
echo "   - Add: bind-address = 0.0.0.0"
echo "   - Restart MySQL: brew services restart mysql"
echo ""
echo "4. Create a user that can connect from any host:"
echo "   mysql -u root -p -e \"CREATE USER 'event_user'@'%' IDENTIFIED BY 'event_password';\""
echo "   mysql -u root -p -e \"GRANT ALL PRIVILEGES ON event_booking_system.* TO 'event_user'@'%';\""
echo "   mysql -u root -p -e \"FLUSH PRIVILEGES;\""
echo ""
echo "⚠️  Security Note: This opens your database to external connections."
echo "   Only do this temporarily and consider using a VPN or tunnel service."
