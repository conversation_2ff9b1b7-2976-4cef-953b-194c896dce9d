<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

echo "<h2>🔐 Login Test Script</h2>";

// Test login functionality
function testLogin($username, $password, $expectedResult = true) {
    global $userManager;
    
    echo "<h3>Testing login: $username</h3>";
    
    // Clear any existing session
    if (session_status() == PHP_SESSION_ACTIVE) {
        session_destroy();
    }
    session_start();
    
    try {
        $result = $userManager->login($username, $password);
        
        if ($result) {
            echo "<p style='color: green;'>✅ Login successful!</p>";
            echo "<ul>";
            echo "<li>User ID: " . ($_SESSION['user_id'] ?? 'Not set') . "</li>";
            echo "<li>Username: " . ($_SESSION['username'] ?? 'Not set') . "</li>";
            echo "<li>Role: " . ($_SESSION['user_role'] ?? 'Not set') . "</li>";
            echo "<li>Name: " . ($_SESSION['user_name'] ?? 'Not set') . "</li>";
            echo "</ul>";
        } else {
            echo "<p style='color: red;'>❌ Login failed!</p>";
        }
        
        return $result;
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Login error: " . htmlspecialchars($e->getMessage()) . "</p>";
        return false;
    }
}

// Test different login scenarios
echo "<h3>🧪 Testing Login Scenarios:</h3>";

$tests = [
    ['admin', 'admin123', true],
    ['testuser', 'user123', true],
    ['<EMAIL>', 'admin123', true], // Email login
    ['<EMAIL>', 'user123', true], // Email login
    ['nonexistent', 'password', false],
    ['admin', 'wrongpassword', false]
];

$passedTests = 0;
$totalTests = count($tests);

foreach ($tests as $test) {
    $username = $test[0];
    $password = $test[1];
    $expected = $test[2];
    
    $result = testLogin($username, $password, $expected);
    
    if (($result && $expected) || (!$result && !$expected)) {
        echo "<p style='color: green;'>✅ Test passed</p>";
        $passedTests++;
    } else {
        echo "<p style='color: red;'>❌ Test failed - Expected: " . ($expected ? 'success' : 'failure') . ", Got: " . ($result ? 'success' : 'failure') . "</p>";
    }
    
    echo "<hr>";
}

echo "<h3>📊 Test Results:</h3>";
echo "<p><strong>Passed: $passedTests / $totalTests</strong></p>";

if ($passedTests == $totalTests) {
    echo "<p style='color: green; font-size: 18px;'>🎉 All login tests passed!</p>";
} else {
    echo "<p style='color: red; font-size: 18px;'>⚠️ Some tests failed. Check the results above.</p>";
}

echo "<h3>🔗 Quick Links:</h3>";
echo "<ul>";
echo "<li><a href='auth/login.php'>Login Page</a></li>";
echo "<li><a href='auth/register.php'>Registration Page</a></li>";
echo "<li><a href='about.php'>About Page (check developer image)</a></li>";
echo "<li><a href='welcome.php'>Welcome Page</a></li>";
echo "</ul>";

echo "<h3>📝 Test Credentials:</h3>";
echo "<ul>";
echo "<li><strong>Admin:</strong> username: admin, password: admin123</li>";
echo "<li><strong>User:</strong> username: testuser, password: user123</li>";
echo "<li><strong>Email login also works:</strong> <EMAIL> / <EMAIL></li>";
echo "</ul>";
?>
