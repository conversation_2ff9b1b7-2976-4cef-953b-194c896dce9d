-- Setup script for local MySQL database
-- Run this script as MySQL root user

-- Create database
CREATE DATABASE IF NOT EXISTS event_booking_system;

-- Create user with same credentials as Docker setup
CREATE USER IF NOT EXISTS 'event_user'@'localhost' IDENTIFIED BY 'event_password';

-- Grant all privileges on the database to the user
GRANT ALL PRIVILEGES ON event_booking_system.* TO 'event_user'@'localhost';

-- Also create user for any host (in case needed)
CREATE USER IF NOT EXISTS 'event_user'@'%' IDENTIFIED BY 'event_password';
GRANT ALL PRIVILEGES ON event_booking_system.* TO 'event_user'@'%';

-- Flush privileges to ensure changes take effect
FLUSH PRIVILEGES;

-- Show databases to confirm creation
SHOW DATABASES;
