# 🔍 FIND YOUR DEPLOYED DATABASE - Step by Step Guide

## 🎯 Your Current Situation
- ✅ **App is running**: https://zara-events-************.us-central1.run.app
- ✅ **Database connected**: But empty (no tables)
- ❌ **Latest deployment failed**: But old version still works
- ❌ **Local MySQL**: Not connected to deployed app

## 🔍 **STEP 1: Check Google Cloud Run Environment Variables**

### **Method A: Google Cloud Console (Recommended)**
1. **Go to**: https://console.cloud.google.com/run
2. **Click on**: `zara-events` service
3. **Go to**: "REVISIONS" tab
4. **Click on**: The working revision (probably zara-events-00004 or earlier)
5. **Scroll down to**: "Environment Variables" section
6. **Look for these variables**:
   ```
   DB_HOST = ?
   DB_USER = ?
   DB_NAME = ?
   DB_PORT = ?
   ```

### **What You'll See and What It Means:**

#### **If DB_HOST contains "railway":**
```
DB_HOST = containers-us-west-xxx.railway.app
```
**→ You're using Railway Database**
**→ Go to**: https://railway.app
**→ Login and find your MySQL project**

#### **If DB_HOST contains "googleapis" or "cloudsql":**
```
DB_HOST = xxx.xxx.xxx.xxx (IP address)
```
**→ You're using Google Cloud SQL**
**→ Go to**: https://console.cloud.google.com/sql

#### **If DB_HOST is empty or not set:**
**→ No database configured**
**→ We need to create one**

## 🚀 **STEP 2: Access Your Database (Based on Step 1)**

### **Option A: Railway Database**
1. **Go to**: https://railway.app
2. **Login** to your account
3. **Find** your MySQL database project
4. **Click** on the MySQL service
5. **Go to**: "Query" tab
6. **Paste the SQL script** I provided earlier

### **Option B: Google Cloud SQL**
1. **Go to**: https://console.cloud.google.com/sql
2. **Click** on your MySQL instance
3. **Go to**: "Query" tab
4. **Paste the SQL script** I provided earlier

### **Option C: No Database Found - Create Railway Database**
1. **Go to**: https://railway.app
2. **Sign up/Login**
3. **Click**: "New Project"
4. **Select**: "Add MySQL"
5. **Wait**: 2-3 minutes for deployment
6. **Get connection details** from "Connect" tab
7. **Update Cloud Run environment variables**

## 📋 **STEP 3: Update Cloud Run Environment Variables (If Needed)**

If you created a new database or found missing variables:

1. **Go to**: https://console.cloud.google.com/run
2. **Click**: `zara-events` service
3. **Click**: "EDIT & DEPLOY NEW REVISION"
4. **Go to**: "Variables & Secrets" tab
5. **Add/Update these variables**:
   ```
   DB_HOST = your_database_host
   DB_USER = your_database_user
   DB_PASS = your_database_password
   DB_NAME = your_database_name
   DB_PORT = 3306
   ```
6. **Click**: "DEPLOY"

## 🗄️ **STEP 4: Run Database Initialization Script**

Once you've found your database service, run this script:

```sql
-- ZARA-Events Database Initialization
CREATE DATABASE IF NOT EXISTS railway;
USE railway;

-- Drop existing tables to start fresh
DROP TABLE IF EXISTS payments;
DROP TABLE IF EXISTS password_reset_tokens;
DROP TABLE IF EXISTS user_sessions;
DROP TABLE IF EXISTS cart;
DROP TABLE IF EXISTS bookings;
DROP TABLE IF EXISTS events;
DROP TABLE IF EXISTS users;

-- Create users table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    role ENUM('user', 'admin') DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create events table
CREATE TABLE events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    event_date DATE NOT NULL,
    event_time TIME NOT NULL,
    venue VARCHAR(200) NOT NULL,
    location VARCHAR(200) NOT NULL,
    organizer VARCHAR(100) NOT NULL,
    organizer_contact VARCHAR(100),
    image_url VARCHAR(500),
    price DECIMAL(10, 2) NOT NULL,
    total_tickets INT NOT NULL,
    available_tickets INT NOT NULL,
    category VARCHAR(50),
    status ENUM('active', 'inactive', 'cancelled') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create bookings table
CREATE TABLE bookings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    event_id INT NOT NULL,
    quantity INT NOT NULL,
    total_amount DECIMAL(10, 2) NOT NULL,
    booking_status ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'pending',
    payment_status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
    booking_reference VARCHAR(50) UNIQUE NOT NULL,
    attendee_name VARCHAR(100) NOT NULL,
    attendee_email VARCHAR(100) NOT NULL,
    attendee_phone VARCHAR(20),
    special_requirements TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create cart table
CREATE TABLE cart (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    event_id INT NOT NULL,
    quantity INT NOT NULL,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create user_sessions table
CREATE TABLE user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    session_token VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create password_reset_tokens table
CREATE TABLE password_reset_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create payments table
CREATE TABLE payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    booking_id INT NOT NULL,
    payment_reference VARCHAR(255) NOT NULL UNIQUE,
    payment_method ENUM('mobile_money', 'bank_transfer', 'cash', 'card') NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'XAF',
    payment_status ENUM('pending', 'processing', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    transaction_id VARCHAR(255),
    payment_gateway VARCHAR(50) DEFAULT 'simulation',
    payment_details JSON,
    processed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert admin user (password: admin123)
INSERT INTO users (username, email, password, first_name, last_name, role) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System', 'Administrator', 'admin');

-- Insert test user (password: user123)
INSERT INTO users (username, email, password, first_name, last_name, role) VALUES
('testuser', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Test', 'User', 'user');

-- Insert sample events
INSERT INTO events (title, description, event_date, event_time, venue, location, organizer, organizer_contact, price, total_tickets, available_tickets, category, image_url) VALUES
('Tech Conference 2024', 'Annual technology conference featuring latest innovations in AI, blockchain, and cloud computing.', '2024-03-15', '09:00:00', 'Convention Center', 'Douala, Cameroon', 'Tech Events Inc', '<EMAIL>', 175000, 500, 500, 'Technology', 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'),
('Music Festival Summer', 'Three-day music festival featuring top artists from around the world.', '2024-06-20', '18:00:00', 'Central Park', 'Yaoundé, Cameroon', 'Music Productions', '<EMAIL>', 87500, 1000, 1000, 'Music', 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'),
('Business Workshop', 'Professional development workshop for entrepreneurs and business leaders.', '2024-04-10', '10:00:00', 'Business Center', 'Libreville, Gabon', 'Business Academy', '<EMAIL>', 58500, 100, 100, 'Business', 'https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'),
('Art Exhibition', 'Contemporary art exhibition featuring works from local and international artists.', '2024-05-05', '14:00:00', 'Art Gallery', 'Bangui, Central African Republic', 'Art Collective', '<EMAIL>', 14500, 200, 200, 'Art', 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'),
('Sports Championship', 'Annual sports championship finals featuring the best teams from across the region.', '2024-07-15', '19:00:00', 'Sports Arena', 'N\'Djamena, Chad', 'Sports League', '<EMAIL>', 43750, 2000, 2000, 'Sports', 'https://images.unsplash.com/photo-1461896836934-ffe607ba8211?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');

-- Verify success
SELECT 'Database setup complete!' as status;
SELECT COUNT(*) as user_count FROM users;
SELECT COUNT(*) as event_count FROM events;
```

## ✅ **STEP 5: Verify Success**

After running the script:
1. **Go to**: https://zara-events-************.us-central1.run.app/test-db-connection.php
2. **Should show**:
   - ✅ Database Connection: SUCCESS
   - ✅ 7 tables listed
   - 👥 Users: 2
   - 📅 Events: 5

## 🧪 **STEP 6: Test Authentication**

**Registration**: https://zara-events-************.us-central1.run.app/auth/register.php
**Login**: https://zara-events-************.us-central1.run.app/auth/login.php

**Test Credentials**:
- Admin: `admin` / `admin123`
- User: `testuser` / `user123`

## 🔧 **STEP 7: Fix Deployment Issues (After Database Works)**

Once database is working, we'll fix the deployment:
1. **Rollback to working revision** in Cloud Run console
2. **Fix container configuration**
3. **Redeploy with correct settings**

## 📞 **What to Share With Me**

After checking Step 1, tell me:
1. **What you found in DB_HOST** (Railway, Cloud SQL, or empty?)
2. **Whether you have Railway/Cloud SQL access**
3. **Any error messages**

**Priority: Get database working first, then fix deployment!** 🎯
