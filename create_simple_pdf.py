#!/usr/bin/env python3
"""
Simple PDF-ready HTML Generator for ZARA-Events User Manual
Creates a clean HTML file optimized for printing to PDF
"""

import os
from datetime import datetime

def create_pdf_ready_html():
    """Create a PDF-ready HTML file from the user manual"""
    
    # File paths
    markdown_file = 'docs/04-USER-MANUAL.md'
    output_html = 'docs/ZARA-Events-User-Manual-PDF-Ready.html'
    
    # Check if markdown file exists
    if not os.path.exists(markdown_file):
        print(f"Error: {markdown_file} not found!")
        return False
    
    try:
        # Read the markdown content
        with open(markdown_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Create HTML document optimized for PDF printing
        html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ZARA-Events User Manual</title>
    <style>
        @page {{
            size: A4;
            margin: 2cm;
        }}
        
        body {{
            font-family: 'Times New Roman', serif;
            line-height: 1.6;
            color: #333;
            font-size: 12pt;
            max-width: none;
            margin: 0;
            padding: 0;
        }}
        
        .cover-page {{
            text-align: center;
            page-break-after: always;
            padding-top: 3cm;
        }}
        
        .cover-title {{
            font-size: 36pt;
            color: #408681;
            margin-bottom: 1cm;
            font-weight: bold;
        }}
        
        .cover-subtitle {{
            font-size: 24pt;
            color: #2c5f5a;
            margin-bottom: 2cm;
        }}
        
        .cover-info {{
            font-size: 14pt;
            margin-bottom: 0.5cm;
        }}
        
        h1 {{
            color: #408681;
            font-size: 20pt;
            margin-top: 1.5cm;
            margin-bottom: 0.5cm;
            page-break-before: auto;
            border-bottom: 2pt solid #408681;
            padding-bottom: 0.2cm;
        }}
        
        h2 {{
            color: #408681;
            font-size: 16pt;
            margin-top: 1cm;
            margin-bottom: 0.4cm;
            border-bottom: 1pt solid #FBF1DF;
            padding-bottom: 0.1cm;
        }}
        
        h3 {{
            color: #2c5f5a;
            font-size: 14pt;
            margin-top: 0.8cm;
            margin-bottom: 0.3cm;
        }}
        
        h4 {{
            color: #2c5f5a;
            font-size: 12pt;
            margin-top: 0.6cm;
            margin-bottom: 0.2cm;
        }}
        
        p {{
            margin-bottom: 0.4cm;
            text-align: justify;
        }}
        
        ul, ol {{
            margin-bottom: 0.5cm;
            padding-left: 1cm;
        }}
        
        li {{
            margin-bottom: 0.2cm;
        }}
        
        strong {{
            color: #408681;
            font-weight: bold;
        }}
        
        .footer {{
            position: fixed;
            bottom: 1cm;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 10pt;
            color: #666;
            border-top: 1pt solid #ccc;
            padding-top: 0.3cm;
        }}
        
        .page-number:after {{
            content: counter(page);
        }}
        
        @media print {{
            .no-print {{
                display: none;
            }}
        }}
    </style>
</head>
<body>
    <!-- Cover Page -->
    <div class="cover-page">
        <div class="cover-title">📅 ZARA-Events</div>
        <div class="cover-subtitle">Complete User Manual</div>
        <div class="cover-info">Your Complete Guide to Event Discovery and Booking</div>
        <div class="cover-info">Generated on {datetime.now().strftime('%B %d, %Y')}</div>
        <br><br>
        <div class="cover-info"><strong>Developed by:</strong> Tayong Fritz Vugah</div>
        <div class="cover-info"><strong>Institution:</strong> ICT University, Yaoundé, Cameroon</div>
        <div class="cover-info"><strong>Contact:</strong> <EMAIL></div>
        <div class="cover-info"><strong>WhatsApp:</strong> +237 651 408 682</div>
    </div>
    
    <!-- Content -->
    <div class="content">
"""
        
        # Process markdown content line by line
        lines = content.split('\n')
        in_list = False
        
        for line in lines:
            line = line.strip()
            
            if not line:
                if in_list:
                    html_content += "</ul>\n"
                    in_list = False
                html_content += "<br>\n"
                continue
            
            # Handle headers
            if line.startswith('# '):
                if in_list:
                    html_content += "</ul>\n"
                    in_list = False
                text = line[2:].strip().replace('👥 ', '').replace('🎯 ', '').replace('📪 ', '').replace('🛒 ', '').replace('📧 ', '').replace('👤 ', '').replace('📞 ', '').replace('📱 ', '').replace('🔒 ', '')
                html_content += f"<h1>{text}</h1>\n"
            elif line.startswith('## '):
                if in_list:
                    html_content += "</ul>\n"
                    in_list = False
                text = line[3:].strip().replace('🎯 ', '').replace('👤 ', '').replace('🎪 ', '').replace('🛒 ', '').replace('📧 ', '').replace('👤 ', '').replace('📞 ', '').replace('📱 ', '').replace('🔒 ', '')
                html_content += f"<h2>{text}</h2>\n"
            elif line.startswith('### '):
                if in_list:
                    html_content += "</ul>\n"
                    in_list = False
                text = line[4:].strip()
                html_content += f"<h3>{text}</h3>\n"
            elif line.startswith('#### '):
                if in_list:
                    html_content += "</ul>\n"
                    in_list = False
                text = line[5:].strip()
                html_content += f"<h4>{text}</h4>\n"
            # Handle lists
            elif line.startswith('- ') or line.startswith('* '):
                if not in_list:
                    html_content += "<ul>\n"
                    in_list = True
                text = line[2:].strip()
                # Handle bold text
                text = text.replace('**', '<strong>').replace('**', '</strong>')
                html_content += f"<li>{text}</li>\n"
            elif line.startswith(('1. ', '2. ', '3. ', '4. ', '5. ')):
                if in_list:
                    html_content += "</ul>\n"
                    in_list = False
                text = line[3:].strip()
                text = text.replace('**', '<strong>').replace('**', '</strong>')
                html_content += f"<p><strong>{line[:2]}</strong> {text}</p>\n"
            # Handle regular paragraphs
            elif line and not line.startswith('#'):
                if in_list:
                    html_content += "</ul>\n"
                    in_list = False
                # Clean up markdown formatting
                text = line.replace('**', '<strong>').replace('**', '</strong>')
                text = text.replace('*', '<em>').replace('*', '</em>')
                html_content += f"<p>{text}</p>\n"
        
        if in_list:
            html_content += "</ul>\n"
        
        # Close HTML
        html_content += """
    </div>
    
    <div class="footer">
        <div>© """ + str(datetime.now().year) + """ ZARA-Events - Developed by Tayong Fritz Vugah | ICT University, Yaoundé, Cameroon</div>
    </div>
</body>
</html>"""
        
        # Save the HTML file
        with open(output_html, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"✅ PDF-ready HTML successfully generated: {output_html}")
        print(f"📄 File size: {os.path.getsize(output_html) / 1024:.1f} KB")
        print("\n📋 To create PDF:")
        print("1. Open the HTML file in Chrome or Safari")
        print("2. Press Cmd+P (Ctrl+P on Windows)")
        print("3. Select 'Save as PDF'")
        print("4. Choose 'More settings' and set margins to 'Minimum'")
        print("5. Enable 'Background graphics' for better appearance")
        print("6. Click 'Save' and choose location")
        return True
        
    except Exception as e:
        print(f"❌ Error generating HTML: {str(e)}")
        return False

if __name__ == "__main__":
    success = create_pdf_ready_html()
    if success:
        print("\n🎉 PDF-ready HTML generation completed successfully!")
        print("📁 You can find the HTML file at: docs/ZARA-Events-User-Manual-PDF-Ready.html")
    else:
        print("\n❌ HTML generation failed. Please check the error messages above.")
