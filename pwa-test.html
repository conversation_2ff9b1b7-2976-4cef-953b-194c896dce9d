<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>PWA Test - ZARA-Events</title>
    
    <!-- PWA Meta Tags -->
    <link rel="manifest" href="/manifest.json">
    <meta name="theme-color" content="#408681">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="ZARA-Events">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .test-card {
            border: 2px solid #dee2e6;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .test-card.success {
            border-color: #28a745;
            background-color: #f8fff9;
        }
        
        .test-card.error {
            border-color: #dc3545;
            background-color: #fff8f8;
        }
        
        .test-result {
            font-weight: bold;
            margin-top: 10px;
        }
        
        .test-result.success {
            color: #28a745;
        }
        
        .test-result.error {
            color: #dc3545;
        }
        
        .hero-section {
            background: linear-gradient(135deg, #408681, #FBF1DF);
            color: white;
            padding: 3rem 0;
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <div class="hero-section">
        <div class="container text-center">
            <h1><i class="fas fa-mobile-alt me-3"></i>ZARA-Events PWA Test</h1>
            <p class="lead">Testing Progressive Web App Features</p>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container my-5">
        <div class="row">
            <div class="col-12">
                <h2 class="mb-4">PWA Feature Tests</h2>
                <p class="text-muted">This page tests all PWA features to ensure they're working correctly.</p>
            </div>
        </div>

        <!-- Test Results -->
        <div class="row g-4">
            <!-- Service Worker Test -->
            <div class="col-md-6">
                <div class="test-card card h-100 p-4" id="sw-test">
                    <h5><i class="fas fa-cog me-2"></i>Service Worker</h5>
                    <p>Tests if service worker is registered and active</p>
                    <button class="btn btn-primary" onclick="testServiceWorker()">Test Service Worker</button>
                    <div class="test-result" id="sw-result"></div>
                </div>
            </div>

            <!-- Manifest Test -->
            <div class="col-md-6">
                <div class="test-card card h-100 p-4" id="manifest-test">
                    <h5><i class="fas fa-file-code me-2"></i>Web App Manifest</h5>
                    <p>Tests if manifest.json is accessible and valid</p>
                    <button class="btn btn-primary" onclick="testManifest()">Test Manifest</button>
                    <div class="test-result" id="manifest-result"></div>
                </div>
            </div>

            <!-- Install Prompt Test -->
            <div class="col-md-6">
                <div class="test-card card h-100 p-4" id="install-test">
                    <h5><i class="fas fa-download me-2"></i>Install Prompt</h5>
                    <p>Tests if app can be installed</p>
                    <button class="btn btn-primary" onclick="testInstallPrompt()">Test Install</button>
                    <div class="test-result" id="install-result"></div>
                </div>
            </div>

            <!-- Offline Test -->
            <div class="col-md-6">
                <div class="test-card card h-100 p-4" id="offline-test">
                    <h5><i class="fas fa-wifi-slash me-2"></i>Offline Functionality</h5>
                    <p>Tests caching and offline capabilities</p>
                    <button class="btn btn-primary" onclick="testOffline()">Test Offline</button>
                    <div class="test-result" id="offline-result"></div>
                </div>
            </div>

            <!-- Push Notifications Test -->
            <div class="col-md-6">
                <div class="test-card card h-100 p-4" id="push-test">
                    <h5><i class="fas fa-bell me-2"></i>Push Notifications</h5>
                    <p>Tests notification permissions and display</p>
                    <button class="btn btn-primary" onclick="testPushNotifications()">Test Notifications</button>
                    <div class="test-result" id="push-result"></div>
                </div>
            </div>

            <!-- App Features Test -->
            <div class="col-md-6">
                <div class="test-card card h-100 p-4" id="features-test">
                    <h5><i class="fas fa-star me-2"></i>App Features</h5>
                    <p>Tests PWA-specific JavaScript features</p>
                    <button class="btn btn-primary" onclick="testAppFeatures()">Test Features</button>
                    <div class="test-result" id="features-result"></div>
                </div>
            </div>
        </div>

        <!-- Overall Status -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <h4>Overall PWA Status</h4>
                        <div id="overall-status" class="mt-3">
                            <button class="btn btn-success btn-lg" onclick="runAllTests()">
                                <i class="fas fa-play me-2"></i>Run All Tests
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <div class="row mt-4">
            <div class="col-12 text-center">
                <a href="/welcome.php" class="btn btn-outline-primary me-3">
                    <i class="fas fa-home me-2"></i>Back to App
                </a>
                <a href="/offline.html" class="btn btn-outline-secondary">
                    <i class="fas fa-wifi-slash me-2"></i>Test Offline Page
                </a>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/pwa-features.js"></script>
    
    <script>
        // Test Functions
        async function testServiceWorker() {
            const resultEl = document.getElementById('sw-result');
            const cardEl = document.getElementById('sw-test');
            
            try {
                if ('serviceWorker' in navigator) {
                    const registration = await navigator.serviceWorker.getRegistration();
                    if (registration) {
                        resultEl.innerHTML = '<i class="fas fa-check"></i> Service Worker Active';
                        resultEl.className = 'test-result success';
                        cardEl.className = 'test-card card h-100 p-4 success';
                    } else {
                        throw new Error('Service Worker not registered');
                    }
                } else {
                    throw new Error('Service Worker not supported');
                }
            } catch (error) {
                resultEl.innerHTML = '<i class="fas fa-times"></i> ' + error.message;
                resultEl.className = 'test-result error';
                cardEl.className = 'test-card card h-100 p-4 error';
            }
        }

        async function testManifest() {
            const resultEl = document.getElementById('manifest-result');
            const cardEl = document.getElementById('manifest-test');
            
            try {
                const response = await fetch('/manifest.json');
                if (response.ok) {
                    const manifest = await response.json();
                    resultEl.innerHTML = '<i class="fas fa-check"></i> Manifest Valid: ' + manifest.name;
                    resultEl.className = 'test-result success';
                    cardEl.className = 'test-card card h-100 p-4 success';
                } else {
                    throw new Error('Manifest not found');
                }
            } catch (error) {
                resultEl.innerHTML = '<i class="fas fa-times"></i> ' + error.message;
                resultEl.className = 'test-result error';
                cardEl.className = 'test-card card h-100 p-4 error';
            }
        }

        function testInstallPrompt() {
            const resultEl = document.getElementById('install-result');
            const cardEl = document.getElementById('install-test');
            
            if (window.pwaManager && window.pwaManager.deferredPrompt) {
                window.pwaManager.promptInstall();
                resultEl.innerHTML = '<i class="fas fa-check"></i> Install prompt triggered';
                resultEl.className = 'test-result success';
                cardEl.className = 'test-card card h-100 p-4 success';
            } else if (window.matchMedia('(display-mode: standalone)').matches) {
                resultEl.innerHTML = '<i class="fas fa-check"></i> App already installed';
                resultEl.className = 'test-result success';
                cardEl.className = 'test-card card h-100 p-4 success';
            } else {
                resultEl.innerHTML = '<i class="fas fa-info"></i> Install prompt not available';
                resultEl.className = 'test-result error';
                cardEl.className = 'test-card card h-100 p-4 error';
            }
        }

        async function testOffline() {
            const resultEl = document.getElementById('offline-result');
            const cardEl = document.getElementById('offline-test');
            
            try {
                // Test if offline page is cached
                const cache = await caches.open('zara-events-v1.0.0');
                const offlineResponse = await cache.match('/offline.html');
                
                if (offlineResponse) {
                    resultEl.innerHTML = '<i class="fas fa-check"></i> Offline page cached';
                    resultEl.className = 'test-result success';
                    cardEl.className = 'test-card card h-100 p-4 success';
                } else {
                    throw new Error('Offline page not cached');
                }
            } catch (error) {
                resultEl.innerHTML = '<i class="fas fa-times"></i> ' + error.message;
                resultEl.className = 'test-result error';
                cardEl.className = 'test-card card h-100 p-4 error';
            }
        }

        async function testPushNotifications() {
            const resultEl = document.getElementById('push-result');
            const cardEl = document.getElementById('push-test');
            
            try {
                if ('Notification' in window) {
                    const permission = await Notification.requestPermission();
                    if (permission === 'granted') {
                        new Notification('ZARA-Events PWA Test', {
                            body: 'Push notifications are working!',
                            icon: '/assets/images/icons/icon-192x192.png'
                        });
                        resultEl.innerHTML = '<i class="fas fa-check"></i> Notifications enabled';
                        resultEl.className = 'test-result success';
                        cardEl.className = 'test-card card h-100 p-4 success';
                    } else {
                        throw new Error('Notification permission denied');
                    }
                } else {
                    throw new Error('Notifications not supported');
                }
            } catch (error) {
                resultEl.innerHTML = '<i class="fas fa-times"></i> ' + error.message;
                resultEl.className = 'test-result error';
                cardEl.className = 'test-card card h-100 p-4 error';
            }
        }

        function testAppFeatures() {
            const resultEl = document.getElementById('features-result');
            const cardEl = document.getElementById('features-test');
            
            const features = [];
            
            if (window.pwaManager) features.push('PWA Manager');
            if (window.modernEventApp) features.push('Modern App');
            if ('serviceWorker' in navigator) features.push('Service Worker Support');
            if (window.matchMedia('(display-mode: standalone)').matches) features.push('Standalone Mode');
            
            if (features.length > 0) {
                resultEl.innerHTML = '<i class="fas fa-check"></i> Features: ' + features.join(', ');
                resultEl.className = 'test-result success';
                cardEl.className = 'test-card card h-100 p-4 success';
            } else {
                resultEl.innerHTML = '<i class="fas fa-times"></i> No PWA features detected';
                resultEl.className = 'test-result error';
                cardEl.className = 'test-card card h-100 p-4 error';
            }
        }

        async function runAllTests() {
            const overallEl = document.getElementById('overall-status');
            overallEl.innerHTML = '<div class="spinner-border text-primary"></div> Running tests...';
            
            await testServiceWorker();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testManifest();
            await new Promise(resolve => setTimeout(resolve, 500));
            testInstallPrompt();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testOffline();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testPushNotifications();
            await new Promise(resolve => setTimeout(resolve, 500));
            testAppFeatures();
            
            // Calculate overall status
            const successTests = document.querySelectorAll('.test-result.success').length;
            const totalTests = document.querySelectorAll('.test-result').length;
            
            if (successTests === totalTests) {
                overallEl.innerHTML = `
                    <div class="alert alert-success">
                        <h5><i class="fas fa-check-circle"></i> All Tests Passed!</h5>
                        <p>Your PWA is fully functional (${successTests}/${totalTests})</p>
                    </div>
                `;
            } else {
                overallEl.innerHTML = `
                    <div class="alert alert-warning">
                        <h5><i class="fas fa-exclamation-triangle"></i> Some Tests Failed</h5>
                        <p>PWA Status: ${successTests}/${totalTests} tests passed</p>
                    </div>
                `;
            }
        }

        // Auto-run basic tests on load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                testServiceWorker();
                testManifest();
            }, 1000);
        });
    </script>
</body>
</html>
