<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

$pageTitle = 'Welcome to ZARA-Events';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>

    <!-- PWA Meta Tags -->
    <meta name="description" content="Discover and book amazing events in Central Africa with ZARA-Events. Your premier platform for concerts, conferences, sports, and entertainment.">
    <meta name="keywords" content="events, booking, Central Africa, concerts, conferences, entertainment, tickets">
    <meta name="author" content="Tayong Fritz Vugah">

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">

    <!-- PWA Theme Colors -->
    <meta name="theme-color" content="#408681">
    <meta name="msapplication-TileColor" content="#408681">
    <meta name="msapplication-navbutton-color" content="#408681">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">

    <!-- PWA App Capabilities -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-title" content="ZARA-Events">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="application-name" content="ZARA-Events">

    <!-- PWA Icons -->
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon.ico">
    <link rel="icon" type="image/png" sizes="192x192" href="/assets/images/icons/icon-192x192.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/images/icons/icon-192x192.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/assets/images/icons/icon-152x152.png">
    <link rel="apple-touch-icon" sizes="144x144" href="/assets/images/icons/icon-144x144.png">
    <link rel="apple-touch-icon" sizes="120x120" href="/assets/images/icons/icon-128x128.png">

    <!-- Microsoft Tiles -->
    <meta name="msapplication-TileImage" content="/assets/images/icons/icon-144x144.png">
    <meta name="msapplication-square70x70logo" content="/assets/images/icons/icon-72x72.png">
    <meta name="msapplication-square150x150logo" content="/assets/images/icons/icon-152x152.png">
    <meta name="msapplication-square310x310logo" content="/assets/images/icons/icon-384x384.png">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="ZARA-Events - Event Booking Platform">
    <meta property="og:description" content="Discover and book amazing events in Central Africa">
    <meta property="og:image" content="/assets/images/icons/icon-512x512.png">
    <meta property="og:url" content="<?php echo SITE_URL; ?>">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="ZARA-Events">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="ZARA-Events - Event Booking Platform">
    <meta name="twitter:description" content="Discover and book amazing events in Central Africa">
    <meta name="twitter:image" content="/assets/images/icons/icon-512x512.png">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="assets/css/modern-ui.css" rel="stylesheet">

    <style>
        .welcome-hero {
            min-height: 100vh;
            background: var(--hero-gradient);
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .welcome-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23FBF1DF" stop-opacity="0.1"/><stop offset="100%" stop-color="%23408681" stop-opacity="0.05"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>');
            opacity: 0.3;
        }

        .welcome-content {
            position: relative;
            z-index: 2;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 10px 40px rgba(64, 134, 129, 0.1);
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 60px rgba(64, 134, 129, 0.2);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: var(--primary-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 2rem;
        }

        .stats-card {
            background: rgba(251, 241, 223, 0.9);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            border: 1px solid rgba(64, 134, 129, 0.1);
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .cta-section {
            background: var(--secondary-gradient);
            padding: 4rem 0;
            position: relative;
        }

        .floating-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }

        .floating-element {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }

        .floating-element:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
        .floating-element:nth-child(2) { top: 20%; right: 10%; animation-delay: 2s; }
        .floating-element:nth-child(3) { bottom: 20%; left: 20%; animation-delay: 4s; }
        .floating-element:nth-child(4) { bottom: 10%; right: 20%; animation-delay: 1s; }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container">
            <a class="navbar-brand" href="welcome.php">
                <i class="fas fa-calendar-star me-2"></i>
                ZARA-Events
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="welcome.php">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#features">Features</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">Contact</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="index.php">
                                <i class="fas fa-calendar-alt me-1"></i>Browse Events
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                <?php echo htmlspecialchars($_SESSION['first_name'] ?? 'User'); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="user/dashboard.php">Dashboard</a></li>
                                <li><a class="dropdown-item" href="user/profile.php">Profile</a></li>
                                <?php if (isAdmin()): ?>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="admin/">Admin Panel</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="auth/logout.php">Logout</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="auth/login.php">Login</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-primary-modern ms-2" href="auth/register.php">Sign Up</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="welcome-hero">
        <div class="floating-elements">
            <i class="fas fa-calendar-alt floating-element" style="font-size: 3rem;"></i>
            <i class="fas fa-ticket-alt floating-element" style="font-size: 2.5rem;"></i>
            <i class="fas fa-music floating-element" style="font-size: 2rem;"></i>
            <i class="fas fa-users floating-element" style="font-size: 3.5rem;"></i>
        </div>

        <div class="container welcome-content">
            <div class="row align-items-center">
                <div class="col-lg-6 mb-5 mb-lg-0">
                    <h1 class="display-3 fw-bold text-white mb-4">
                        Discover Amazing
                        <span style="background: var(--secondary-gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                            Events
                        </span>
                        Near You
                    </h1>
                    <p class="lead text-white-50 mb-4">
                        Join thousands of event enthusiasts and discover the best concerts, workshops, conferences, and social gatherings in your area. Your next unforgettable experience is just a click away.
                    </p>

                    <?php if (!isLoggedIn()): ?>
                        <div class="d-flex gap-3 mb-4">
                            <a href="auth/register.php" class="btn btn-primary-modern btn-lg">
                                <i class="fas fa-rocket me-2"></i>
                                Get Started Free
                            </a>
                            <a href="auth/login.php" class="btn btn-secondary-modern btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                Sign In
                            </a>
                        </div>
                        <p class="text-white-50 small">
                            <i class="fas fa-shield-alt me-1"></i>
                            Free to join • No credit card required • Instant access
                        </p>
                    <?php else: ?>
                        <div class="d-flex gap-3 mb-4">
                            <a href="index.php" class="btn btn-primary-modern btn-lg">
                                <i class="fas fa-calendar-alt me-2"></i>
                                Browse Events
                            </a>
                            <a href="user/dashboard.php" class="btn btn-secondary-modern btn-lg">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                My Dashboard
                            </a>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="col-lg-6">
                    <div class="row g-3">
                        <div class="col-6">
                            <div class="stats-card">
                                <div class="stats-number">500+</div>
                                <div class="text-muted">Events Monthly</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stats-card">
                                <div class="stats-number">10K+</div>
                                <div class="text-muted">Happy Users</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stats-card">
                                <div class="stats-number">50+</div>
                                <div class="text-muted">Cities</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stats-card">
                                <div class="stats-number">24/7</div>
                                <div class="text-muted">Support</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-5" style="background: var(--secondary-light);">
        <div class="container">
            <div class="row mb-5">
                <div class="col-12 text-center">
                    <h2 class="display-5 fw-bold mb-3" style="color: var(--primary-color);">
                        Why Choose ZARA-Events?
                    </h2>
                    <p class="lead text-muted">
                        Everything you need to discover, book, and enjoy amazing events
                    </p>
                </div>
            </div>

            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Smart Discovery</h4>
                        <p class="text-muted">
                            Advanced search and filtering to find events that match your interests, location, and schedule perfectly.
                        </p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Instant Booking</h4>
                        <p class="text-muted">
                            Quick and secure booking process with instant confirmation and digital tickets delivered to your email.
                        </p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Secure Payments</h4>
                        <p class="text-muted">
                            Multiple payment options including mobile money, bank transfer, and cash payments with full security.
                        </p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Mobile Friendly</h4>
                        <p class="text-muted">
                            Fully responsive design that works perfectly on all devices - desktop, tablet, and mobile.
                        </p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Smart Notifications</h4>
                        <p class="text-muted">
                            Get notified about new events, booking confirmations, and important updates via email and SMS.
                        </p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-headset"></i>
                        </div>
                        <h4 class="fw-bold mb-3">24/7 Support</h4>
                        <p class="text-muted">
                            Round-the-clock customer support to help you with any questions or issues you might have.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="container text-center">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <h2 class="display-5 fw-bold mb-4" style="color: var(--primary-color);">
                        Ready to Start Your Event Journey?
                    </h2>
                    <p class="lead mb-4" style="color: var(--primary-dark);">
                        Join thousands of event enthusiasts and never miss out on amazing experiences again.
                    </p>

                    <?php if (!isLoggedIn()): ?>
                        <div class="d-flex justify-content-center gap-3">
                            <a href="auth/register.php" class="btn btn-primary-modern btn-lg">
                                <i class="fas fa-user-plus me-2"></i>
                                Create Free Account
                            </a>
                            <a href="auth/login.php" class="btn btn-secondary-modern btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                Sign In
                            </a>
                        </div>
                    <?php else: ?>
                        <a href="index.php" class="btn btn-primary-modern btn-lg">
                            <i class="fas fa-calendar-alt me-2"></i>
                            Explore Events Now
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-4" style="background: var(--primary-color); color: white;">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-calendar-star me-2 fs-4"></i>
                        <span class="fw-bold">ZARA-Events</span>
                    </div>
                    <p class="mb-0 mt-2 text-white-50">
                        Discover amazing events near you
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        &copy; <?php echo date('Y'); ?> ZARA-Events. All rights reserved.
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="assets/js/modern-app.js"></script>

    <!-- PWA Features -->
    <script src="assets/js/pwa-features.js"></script>

    <!-- PWA Installation Script -->
    <script>
        // PWA Installation and Features
        document.addEventListener('DOMContentLoaded', function() {
            // Check if PWA is supported
            if ('serviceWorker' in navigator) {
                console.log('PWA: Service Worker supported');

                // Register service worker
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('PWA: Service Worker registered successfully');
                    })
                    .catch(error => {
                        console.error('PWA: Service Worker registration failed:', error);
                    });
            }

            // Add PWA install prompt to CTA buttons if not installed
            setTimeout(() => {
                if (window.pwaManager && !window.pwaManager.isInstalled) {
                    const ctaButtons = document.querySelectorAll('.btn-primary-modern');
                    ctaButtons.forEach(btn => {
                        if (btn.textContent.includes('Get Started') || btn.textContent.includes('Create Free Account')) {
                            const originalText = btn.innerHTML;
                            btn.innerHTML = '<i class="fas fa-mobile-alt me-2"></i>Install App & Get Started';
                            btn.onclick = (e) => {
                                e.preventDefault();
                                if (window.pwaManager) {
                                    window.pwaManager.promptInstall();
                                }
                            };
                        }
                    });
                }
            }, 2000);

            // Add app-like behaviors
            addAppBehaviors();
        });

        function addAppBehaviors() {
            // Prevent context menu on long press (mobile)
            document.addEventListener('contextmenu', function(e) {
                if (window.matchMedia('(display-mode: standalone)').matches) {
                    e.preventDefault();
                }
            });

            // Add haptic feedback for buttons (if supported)
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('btn') && 'vibrate' in navigator) {
                    navigator.vibrate(50);
                }
            });

            // Smooth scroll for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        }

        // Add install banner for supported devices
        function showInstallBanner() {
            const banner = document.createElement('div');
            banner.className = 'install-banner';
            banner.innerHTML = `
                <div class="container">
                    <div class="row align-items-center">
                        <div class="col-auto">
                            <i class="fas fa-mobile-alt fa-2x text-primary"></i>
                        </div>
                        <div class="col">
                            <h6 class="mb-1">Install ZARA-Events App</h6>
                            <small class="text-muted">Get the full app experience on your device</small>
                        </div>
                        <div class="col-auto">
                            <button class="btn btn-primary btn-sm" onclick="installApp()">Install</button>
                            <button class="btn btn-outline-secondary btn-sm ms-2" onclick="dismissBanner()">Later</button>
                        </div>
                    </div>
                </div>
            `;

            banner.style.cssText = `
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                background: white;
                border-top: 1px solid #dee2e6;
                padding: 1rem 0;
                z-index: 1050;
                box-shadow: 0 -4px 12px rgba(0,0,0,0.1);
                transform: translateY(100%);
                transition: transform 0.3s ease;
            `;

            document.body.appendChild(banner);

            // Show banner with animation
            setTimeout(() => {
                banner.style.transform = 'translateY(0)';
            }, 1000);

            // Auto-hide after 10 seconds
            setTimeout(() => {
                dismissBanner();
            }, 10000);
        }

        function installApp() {
            if (window.pwaManager) {
                window.pwaManager.promptInstall();
            }
            dismissBanner();
        }

        function dismissBanner() {
            const banner = document.querySelector('.install-banner');
            if (banner) {
                banner.style.transform = 'translateY(100%)';
                setTimeout(() => {
                    banner.remove();
                }, 300);
            }
        }

        // Show install banner for non-installed users
        setTimeout(() => {
            if (window.pwaManager && !window.pwaManager.isInstalled && window.pwaManager.deferredPrompt) {
                showInstallBanner();
            }
        }, 3000);
    </script>
</body>
</html>
