#!/usr/bin/env python3
"""
ZARA-Events Railway Database Setup Script
This script will connect to your Railway database and set up all tables and data.
"""

import mysql.connector
import sys

# Your Railway database connection details
DB_CONFIG = {
    'host': 'hopper.proxy.rlwy.net',
    'port': 46358,
    'user': 'root',
    'password': 'lrQhHqRUqUoOfphwkfLlFeJRYNCNJkzc',
    'database': 'railway'
}

# SQL script to set up the database
SQL_SCRIPT = """
-- ZARA-Events Database Setup
USE railway;

-- Drop existing tables to start fresh
DROP TABLE IF EXISTS payments;
DROP TABLE IF EXISTS password_reset_tokens;
DROP TABLE IF EXISTS user_sessions;
DROP TABLE IF EXISTS cart;
DROP TABLE IF EXISTS bookings;
DROP TABLE IF EXISTS events;
DROP TABLE IF EXISTS users;

-- Create users table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHA<PERSON>(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    role ENUM('user', 'admin') DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create events table
CREATE TABLE events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    event_date DATE NOT NULL,
    event_time TIME NOT NULL,
    venue VARCHAR(200) NOT NULL,
    location VARCHAR(200) NOT NULL,
    organizer VARCHAR(100) NOT NULL,
    organizer_contact VARCHAR(100),
    image_url VARCHAR(500),
    price DECIMAL(10, 2) NOT NULL,
    total_tickets INT NOT NULL,
    available_tickets INT NOT NULL,
    category VARCHAR(50),
    status ENUM('active', 'inactive', 'cancelled') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create bookings table
CREATE TABLE bookings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    event_id INT NOT NULL,
    quantity INT NOT NULL,
    total_amount DECIMAL(10, 2) NOT NULL,
    booking_status ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'pending',
    payment_status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
    booking_reference VARCHAR(50) UNIQUE NOT NULL,
    attendee_name VARCHAR(100) NOT NULL,
    attendee_email VARCHAR(100) NOT NULL,
    attendee_phone VARCHAR(20),
    special_requirements TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create cart table
CREATE TABLE cart (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    event_id INT NOT NULL,
    quantity INT NOT NULL,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create user_sessions table
CREATE TABLE user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    session_token VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create password_reset_tokens table
CREATE TABLE password_reset_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create payments table
CREATE TABLE payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    booking_id INT NOT NULL,
    payment_reference VARCHAR(255) NOT NULL UNIQUE,
    payment_method ENUM('mobile_money', 'bank_transfer', 'cash', 'card') NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'XAF',
    payment_status ENUM('pending', 'processing', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    transaction_id VARCHAR(255),
    payment_gateway VARCHAR(50) DEFAULT 'simulation',
    payment_details JSON,
    processed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert admin user (password: admin123)
INSERT INTO users (username, email, password, first_name, last_name, role) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System', 'Administrator', 'admin');

-- Insert test user (password: user123)
INSERT INTO users (username, email, password, first_name, last_name, role) VALUES
('testuser', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Test', 'User', 'user');

-- Insert sample events
INSERT INTO events (title, description, event_date, event_time, venue, location, organizer, organizer_contact, price, total_tickets, available_tickets, category, image_url) VALUES
('Tech Conference 2024', 'Annual technology conference featuring latest innovations in AI, blockchain, and cloud computing.', '2024-03-15', '09:00:00', 'Convention Center', 'Douala, Cameroon', 'Tech Events Inc', '<EMAIL>', 175000, 500, 500, 'Technology', 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'),
('Music Festival Summer', 'Three-day music festival featuring top artists from around the world.', '2024-06-20', '18:00:00', 'Central Park', 'Yaoundé, Cameroon', 'Music Productions', '<EMAIL>', 87500, 1000, 1000, 'Music', 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'),
('Business Workshop', 'Professional development workshop for entrepreneurs and business leaders.', '2024-04-10', '10:00:00', 'Business Center', 'Libreville, Gabon', 'Business Academy', '<EMAIL>', 58500, 100, 100, 'Business', 'https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'),
('Art Exhibition', 'Contemporary art exhibition featuring works from local and international artists.', '2024-05-05', '14:00:00', 'Art Gallery', 'Bangui, Central African Republic', 'Art Collective', '<EMAIL>', 14500, 200, 200, 'Art', 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'),
('Sports Championship', 'Annual sports championship finals featuring the best teams from across the region.', '2024-07-15', '19:00:00', 'Sports Arena', 'N\'Djamena, Chad', 'Sports League', '<EMAIL>', 43750, 2000, 2000, 'Sports', 'https://images.unsplash.com/photo-1461896836934-ffe607ba8211?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');
"""

def setup_database():
    """Set up the Railway database for ZARA-Events"""
    print("🚀 ZARA-Events Database Setup")
    print("=" * 40)
    
    try:
        # Connect to Railway database
        print("📡 Connecting to Railway database...")
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        print("✅ Connected successfully!")
        
        # Execute SQL script
        print("🔧 Setting up database tables and data...")

        # Execute each statement individually with better error handling
        sql_statements = [
            "USE railway",

            # Drop tables
            "DROP TABLE IF EXISTS payments",
            "DROP TABLE IF EXISTS password_reset_tokens",
            "DROP TABLE IF EXISTS user_sessions",
            "DROP TABLE IF EXISTS cart",
            "DROP TABLE IF EXISTS bookings",
            "DROP TABLE IF EXISTS events",
            "DROP TABLE IF EXISTS users",

            # Create users table
            """CREATE TABLE users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                first_name VARCHAR(50) NOT NULL,
                last_name VARCHAR(50) NOT NULL,
                phone VARCHAR(20),
                address TEXT,
                role ENUM('user', 'admin') DEFAULT 'user',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )""",

            # Create events table
            """CREATE TABLE events (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(200) NOT NULL,
                description TEXT,
                event_date DATE NOT NULL,
                event_time TIME NOT NULL,
                venue VARCHAR(200) NOT NULL,
                location VARCHAR(200) NOT NULL,
                organizer VARCHAR(100) NOT NULL,
                organizer_contact VARCHAR(100),
                image_url VARCHAR(500),
                price DECIMAL(10, 2) NOT NULL,
                total_tickets INT NOT NULL,
                available_tickets INT NOT NULL,
                category VARCHAR(50),
                status ENUM('active', 'inactive', 'cancelled') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )""",

            # Create other tables
            """CREATE TABLE bookings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                event_id INT NOT NULL,
                quantity INT NOT NULL,
                total_amount DECIMAL(10, 2) NOT NULL,
                booking_status ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'pending',
                payment_status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
                booking_reference VARCHAR(50) UNIQUE NOT NULL,
                attendee_name VARCHAR(100) NOT NULL,
                attendee_email VARCHAR(100) NOT NULL,
                attendee_phone VARCHAR(20),
                special_requirements TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )""",

            """CREATE TABLE cart (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                event_id INT NOT NULL,
                quantity INT NOT NULL,
                added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )""",

            """CREATE TABLE user_sessions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                session_token VARCHAR(255) NOT NULL,
                expires_at TIMESTAMP NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )""",

            """CREATE TABLE password_reset_tokens (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                token VARCHAR(255) NOT NULL,
                expires_at TIMESTAMP NOT NULL,
                used BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )""",

            """CREATE TABLE payments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                booking_id INT NOT NULL,
                payment_reference VARCHAR(255) NOT NULL UNIQUE,
                payment_method ENUM('mobile_money', 'bank_transfer', 'cash', 'card') NOT NULL,
                amount DECIMAL(10, 2) NOT NULL,
                currency VARCHAR(3) DEFAULT 'XAF',
                payment_status ENUM('pending', 'processing', 'completed', 'failed', 'refunded') DEFAULT 'pending',
                transaction_id VARCHAR(255),
                payment_gateway VARCHAR(50) DEFAULT 'simulation',
                payment_details JSON,
                processed_at TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )""",

            # Insert data
            """INSERT INTO users (username, email, password, first_name, last_name, role) VALUES
            ('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System', 'Administrator', 'admin')""",

            """INSERT INTO users (username, email, password, first_name, last_name, role) VALUES
            ('testuser', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Test', 'User', 'user')""",
        ]

        # Add event inserts
        event_inserts = [
            """INSERT INTO events (title, description, event_date, event_time, venue, location, organizer, organizer_contact, price, total_tickets, available_tickets, category, image_url) VALUES
            ('Tech Conference 2024', 'Annual technology conference featuring latest innovations in AI, blockchain, and cloud computing.', '2024-03-15', '09:00:00', 'Convention Center', 'Douala, Cameroon', 'Tech Events Inc', '<EMAIL>', 175000, 500, 500, 'Technology', 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80')""",

            """INSERT INTO events (title, description, event_date, event_time, venue, location, organizer, organizer_contact, price, total_tickets, available_tickets, category, image_url) VALUES
            ('Music Festival Summer', 'Three-day music festival featuring top artists from around the world.', '2024-06-20', '18:00:00', 'Central Park', 'Yaoundé, Cameroon', 'Music Productions', '<EMAIL>', 87500, 1000, 1000, 'Music', 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80')""",

            """INSERT INTO events (title, description, event_date, event_time, venue, location, organizer, organizer_contact, price, total_tickets, available_tickets, category, image_url) VALUES
            ('Business Workshop', 'Professional development workshop for entrepreneurs and business leaders.', '2024-04-10', '10:00:00', 'Business Center', 'Libreville, Gabon', 'Business Academy', '<EMAIL>', 58500, 100, 100, 'Business', 'https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80')""",

            """INSERT INTO events (title, description, event_date, event_time, venue, location, organizer, organizer_contact, price, total_tickets, available_tickets, category, image_url) VALUES
            ('Art Exhibition', 'Contemporary art exhibition featuring works from local and international artists.', '2024-05-05', '14:00:00', 'Art Gallery', 'Bangui, Central African Republic', 'Art Collective', '<EMAIL>', 14500, 200, 200, 'Art', 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80')""",

            """INSERT INTO events (title, description, event_date, event_time, venue, location, organizer, organizer_contact, price, total_tickets, available_tickets, category, image_url) VALUES
            ('Sports Championship', 'Annual sports championship finals featuring the best teams from across the region.', '2024-07-15', '19:00:00', 'Sports Arena', 'N\\'Djamena, Chad', 'Sports League', '<EMAIL>', 43750, 2000, 2000, 'Sports', 'https://images.unsplash.com/photo-1461896836934-ffe607ba8211?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80')"""
        ]

        sql_statements.extend(event_inserts)

        for i, statement in enumerate(sql_statements):
            try:
                cursor.execute(statement)
                print(f"✅ Executed statement {i+1}/{len(sql_statements)}")
                connection.commit()  # Commit after each statement
            except mysql.connector.Error as e:
                print(f"❌ Error on statement {i+1}: {e}")
                print(f"Statement: {statement[:100]}...")
                # Continue with other statements
        
        # Verify setup
        print("\n🔍 Verifying database setup...")
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        print(f"✅ Tables created: {len(tables)}")
        
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        print(f"✅ Users: {user_count}")
        
        cursor.execute("SELECT COUNT(*) FROM events")
        event_count = cursor.fetchone()[0]
        print(f"✅ Events: {event_count}")
        
        print("\n🎉 Database setup completed successfully!")
        print("\nTest credentials:")
        print("Admin: admin / admin123")
        print("User: testuser / user123")
        
    except mysql.connector.Error as e:
        print(f"❌ Database error: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()
            print("📡 Database connection closed")

if __name__ == "__main__":
    setup_database()
