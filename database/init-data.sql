-- Additional initialization data for Event Booking System
-- This file ensures sample data is loaded after schema creation

USE event_booking_system;

-- Insert sample admin user (password: admin123)
-- Only insert if not exists to avoid duplicates
INSERT IGNORE INTO users (username, email, password, first_name, last_name, role) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System', 'Administrator', 'admin');

-- Insert sample regular user (password: user123)
INSERT IGNORE INTO users (username, email, password, first_name, last_name, role) VALUES
('testuser', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Test', 'User', 'user');

-- Insert sample events (only if table is empty)
INSERT INTO events (title, description, event_date, event_time, venue, location, organizer, organizer_contact, price, total_tickets, available_tickets, category, image_url)
SELECT * FROM (
    SELECT 'Tech Conference 2024' as title, 'Annual technology conference featuring latest innovations in AI, blockchain, and cloud computing. Join industry leaders and innovators for three days of learning and networking.' as description, '2024-03-15' as event_date, '09:00:00' as event_time, 'Convention Center' as venue, 'Douala, Cameroon' as location, 'Tech Events Inc' as organizer, '<EMAIL>' as organizer_contact, 175000 as price, 500 as total_tickets, 500 as available_tickets, 'Technology' as category, 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80' as image_url
    UNION ALL
    SELECT 'Music Festival Summer', 'Three-day music festival featuring top artists from around the world. Experience live performances, food trucks, and art installations in a beautiful outdoor setting.', '2024-06-20', '18:00:00', 'Central Park', 'Yaoundé, Cameroon' as location, 'Music Productions', '<EMAIL>', 87500, 1000, 1000, 'Music', 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
    UNION ALL
    SELECT 'Business Workshop', 'Professional development workshop for entrepreneurs and business leaders. Learn about digital marketing, financial planning, and leadership strategies.', '2024-04-10', '10:00:00', 'Business Center', 'Libreville, Gabon' as location, 'Business Academy', '<EMAIL>', 58500, 100, 100, 'Business', 'https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
    UNION ALL
    SELECT 'Art Exhibition', 'Contemporary art exhibition featuring works from local and international artists. Explore diverse mediums including paintings, sculptures, and digital art.', '2024-05-05', '14:00:00', 'Art Gallery', 'Bangui, Central African Republic', 'Art Collective', '<EMAIL>', 14500, 200, 200, 'Art', 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
    UNION ALL
    SELECT 'Sports Championship', 'Annual sports championship finals featuring the best teams from across the region. Experience the excitement of live sports with family and friends.', '2024-07-15', '19:00:00', 'Sports Arena', 'N\'Djamena, Chad', 'Sports League', '<EMAIL>', 43750, 2000, 2000, 'Sports', 'https://images.unsplash.com/photo-1461896836934-ffe607ba8211?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
    UNION ALL
    SELECT 'Cooking Masterclass', 'Learn from professional chefs in this hands-on cooking experience. Master the art of Central African cuisine with authentic recipes and techniques.', '2024-04-25', '16:00:00', 'Culinary Institute', 'Malabo, Equatorial Guinea', 'Chef Academy', '<EMAIL>', 73000, 50, 50, 'Food', 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
    UNION ALL
    SELECT 'Photography Workshop', 'Improve your photography skills with professional photographers. Learn about composition, lighting, and post-processing techniques.', '2024-05-18', '11:00:00', 'Photo Studio', 'Brazzaville, Republic of the Congo', 'Photo Masters', '<EMAIL>', 52500, 75, 75, 'Education', 'https://images.unsplash.com/photo-1606983340126-99ab4feaa64a?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
    UNION ALL
    SELECT 'Startup Pitch Night', 'Watch innovative startups pitch their ideas to investors and industry experts. Network with entrepreneurs and learn about the latest business trends.', '2024-06-08', '19:30:00', 'Innovation Hub', 'Kinshasa, Democratic Republic of the Congo', 'Startup Community', '<EMAIL>', 20500, 150, 150, 'Business', 'https://images.unsplash.com/photo-1559136555-9303baea8ebd?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
) AS tmp
WHERE NOT EXISTS (SELECT 1 FROM events LIMIT 1);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_events_date ON events(event_date);
CREATE INDEX IF NOT EXISTS idx_events_category ON events(category);
CREATE INDEX IF NOT EXISTS idx_events_status ON events(status);
CREATE INDEX IF NOT EXISTS idx_bookings_user ON bookings(user_id);
CREATE INDEX IF NOT EXISTS idx_bookings_event ON bookings(event_id);
CREATE INDEX IF NOT EXISTS idx_cart_user ON cart(user_id);

-- Set proper permissions and ensure database is ready
FLUSH PRIVILEGES;
