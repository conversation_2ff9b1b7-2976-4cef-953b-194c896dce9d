#!/usr/bin/env python3
"""
Alternative PDF Generator for ZARA-Events User Manual
Uses reportlab to create a PDF directly from the markdown content
"""

import os
import sys
from datetime import datetime

try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib.colors import HexColor
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

def install_reportlab():
    """Install reportlab package"""
    print("Installing reportlab...")
    os.system("pip3 install reportlab")

def create_pdf_with_reportlab():
    """Create PDF using reportlab"""
    
    if not REPORTLAB_AVAILABLE:
        print("reportlab not found. Installing...")
        install_reportlab()
        print("Please run the script again after installation.")
        return False
    
    # File paths
    markdown_file = 'docs/04-USER-MANUAL.md'
    output_pdf = 'docs/ZARA-Events-User-Manual.pdf'
    
    # Check if markdown file exists
    if not os.path.exists(markdown_file):
        print(f"Error: {markdown_file} not found!")
        return False
    
    try:
        # Read the markdown content
        with open(markdown_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Create PDF document
        doc = SimpleDocTemplate(
            output_pdf,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )
        
        # Define styles
        styles = getSampleStyleSheet()
        
        # Custom styles
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            textColor=HexColor('#408681'),
            alignment=1  # Center alignment
        )
        
        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=12,
            textColor=HexColor('#408681')
        )
        
        subheading_style = ParagraphStyle(
            'CustomSubHeading',
            parent=styles['Heading3'],
            fontSize=14,
            spaceAfter=10,
            textColor=HexColor('#2c5f5a')
        )
        
        normal_style = ParagraphStyle(
            'CustomNormal',
            parent=styles['Normal'],
            fontSize=11,
            spaceAfter=6,
            alignment=4  # Justify
        )
        
        # Build story (content)
        story = []
        
        # Title page
        story.append(Spacer(1, 2*inch))
        story.append(Paragraph("📅 ZARA-Events", title_style))
        story.append(Paragraph("Complete User Manual", heading_style))
        story.append(Spacer(1, 0.5*inch))
        story.append(Paragraph("Your Complete Guide to Event Discovery and Booking", normal_style))
        story.append(Spacer(1, 0.3*inch))
        story.append(Paragraph(f"Generated on {datetime.now().strftime('%B %d, %Y')}", normal_style))
        story.append(Spacer(1, 1*inch))
        story.append(Paragraph("Developed by Tayong Fritz Vugah", normal_style))
        story.append(Paragraph("ICT University, Yaoundé, Cameroon", normal_style))
        story.append(Paragraph("Contact: <EMAIL>", normal_style))
        story.append(PageBreak())
        
        # Process markdown content
        lines = content.split('\n')
        
        for line in lines:
            line = line.strip()
            
            if not line:
                story.append(Spacer(1, 6))
                continue
            
            # Handle headers
            if line.startswith('# '):
                text = line[2:].strip()
                story.append(Paragraph(text, title_style))
            elif line.startswith('## '):
                text = line[3:].strip()
                story.append(Paragraph(text, heading_style))
            elif line.startswith('### '):
                text = line[4:].strip()
                story.append(Paragraph(text, subheading_style))
            elif line.startswith('#### '):
                text = line[5:].strip()
                story.append(Paragraph(text, subheading_style))
            # Handle lists
            elif line.startswith('- ') or line.startswith('* '):
                text = "• " + line[2:].strip()
                story.append(Paragraph(text, normal_style))
            elif line.startswith('1. ') or line.startswith('2. ') or line.startswith('3. '):
                story.append(Paragraph(line, normal_style))
            # Handle regular paragraphs
            elif line and not line.startswith('#'):
                # Clean up markdown formatting
                text = line.replace('**', '<b>').replace('**', '</b>')
                text = text.replace('*', '<i>').replace('*', '</i>')
                story.append(Paragraph(text, normal_style))
        
        # Build PDF
        print("Generating PDF with reportlab...")
        doc.build(story)
        
        print(f"✅ PDF successfully generated: {output_pdf}")
        print(f"📄 File size: {os.path.getsize(output_pdf) / 1024:.1f} KB")
        return True
        
    except Exception as e:
        print(f"❌ Error generating PDF: {str(e)}")
        return False

if __name__ == "__main__":
    if not REPORTLAB_AVAILABLE:
        print("Installing required package...")
        install_reportlab()
        print("Please run the script again.")
        sys.exit(1)
    
    success = create_pdf_with_reportlab()
    if success:
        print("\n🎉 PDF generation completed successfully!")
        print("📁 You can find the PDF at: docs/ZARA-Events-User-Manual.pdf")
    else:
        print("\n❌ PDF generation failed. Please check the error messages above.")
        sys.exit(1)
