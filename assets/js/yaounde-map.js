/**
 * Yaoundé Map Integration for ZARA-Events
 * Displays the Yaoundé Urban Community boundary using OpenStreetMap data
 */

class YaoundeMap {
    constructor() {
        this.relationId = '2746229'; // Communauté urbaine de Yaoundé
        this.overpassUrl = 'https://overpass-api.de/api/interpreter';
        this.nominatimUrl = 'https://nominatim.openstreetmap.org';
    }

    /**
     * Initialize map with Yaoundé boundary
     * @param {string} containerId - ID of the map container
     * @param {Object} options - Map configuration options
     */
    async initializeMap(containerId, options = {}) {
        console.log(`YaoundeMap: Initializing map for container: ${containerId}`);

        const defaultOptions = {
            center: [3.8480, 11.5021],
            zoom: 11,
            showBoundary: true,
            showLandmarks: true,
            showServiceArea: true
        };

        const config = { ...defaultOptions, ...options };
        console.log('YaoundeMap: Configuration:', config);

        try {
            // Check if container exists
            const container = document.getElementById(containerId);
            if (!container) {
                throw new Error(`Container with ID '${containerId}' not found`);
            }

            console.log('YaoundeMap: Container found, creating map...');

            // Initialize Leaflet map
            const map = L.map(containerId).setView(config.center, config.zoom);
            console.log('YaoundeMap: Leaflet map created');

            // Add OpenStreetMap tiles
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                maxZoom: 18
            }).addTo(map);
            console.log('YaoundeMap: Tile layer added');

            try {
                if (config.showBoundary) {
                    console.log('YaoundeMap: Adding boundary...');
                    await this.addYaoundeBoundary(map);
                }

                if (config.showLandmarks) {
                    console.log('YaoundeMap: Adding landmarks...');
                    this.addKeyLandmarks(map);
                }

                if (config.showServiceArea) {
                    console.log('YaoundeMap: Adding service area...');
                    this.addServiceAreaInfo(map);
                }

            } catch (error) {
                console.warn('Could not load detailed boundary data, using simplified version:', error);
                this.addSimplifiedBoundary(map);
            }

            // Hide loading indicator if it exists
            const loadingIndicator = document.getElementById('mapLoadingIndicator');
            if (loadingIndicator) {
                loadingIndicator.style.display = 'none';
            }

            console.log('YaoundeMap: Map initialization completed successfully');
            return map;

        } catch (error) {
            console.error('YaoundeMap: Error initializing map:', error);
            throw error;
        }
    }

    /**
     * Fetch and display the actual Yaoundé Urban Community boundary
     */
    async addYaoundeBoundary(map) {
        try {
            // Overpass query to get the relation geometry
            const query = `
                [out:json][timeout:25];
                (
                  relation(${this.relationId});
                );
                out geom;
            `;

            const response = await fetch(this.overpassUrl, {
                method: 'POST',
                body: query,
                headers: {
                    'Content-Type': 'text/plain'
                }
            });

            if (!response.ok) {
                throw new Error('Failed to fetch boundary data');
            }

            const data = await response.json();
            
            if (data.elements && data.elements.length > 0) {
                const relation = data.elements[0];
                
                // Process the relation members to create boundary
                const coordinates = this.processRelationGeometry(relation);
                
                if (coordinates.length > 0) {
                    const boundary = L.polygon(coordinates, {
                        color: '#007bff',
                        weight: 3,
                        opacity: 0.8,
                        fillColor: '#007bff',
                        fillOpacity: 0.2
                    }).addTo(map);

                    boundary.bindPopup(`
                        <div class="text-center">
                            <strong><i class="fas fa-map-marked-alt me-2"></i>Communauté urbaine de Yaoundé</strong><br>
                            <small>Yaoundé Urban Community</small><br>
                            <small class="text-muted">ZARA-Events Service Area</small>
                        </div>
                    `);

                    // Fit map to boundary
                    map.fitBounds(boundary.getBounds(), { padding: [20, 20] });
                }
            }

        } catch (error) {
            console.error('Error fetching boundary:', error);
            throw error;
        }
    }

    /**
     * Process relation geometry from Overpass API response
     */
    processRelationGeometry(relation) {
        const coordinates = [];
        
        if (relation.members) {
            relation.members.forEach(member => {
                if (member.type === 'way' && member.geometry) {
                    const wayCoords = member.geometry.map(node => [node.lat, node.lon]);
                    coordinates.push(...wayCoords);
                }
            });
        }

        return coordinates;
    }

    /**
     * Add simplified boundary if API fails
     */
    addSimplifiedBoundary(map) {
        // Simplified polygon representing Yaoundé Urban Community
        const yaoundeBoundary = [
            [3.7200, 11.3800],
            [3.7200, 11.6200],
            [3.9800, 11.6200],
            [3.9800, 11.3800],
            [3.7200, 11.3800]
        ];

        const boundary = L.polygon(yaoundeBoundary, {
            color: '#007bff',
            weight: 3,
            opacity: 0.8,
            fillColor: '#007bff',
            fillOpacity: 0.2
        }).addTo(map);

        boundary.bindPopup(`
            <div class="text-center">
                <strong><i class="fas fa-map-marked-alt me-2"></i>Yaoundé Urban Community</strong><br>
                <small>Approximate Service Area</small><br>
                <small class="text-muted">ZARA-Events Coverage</small>
            </div>
        `);

        map.fitBounds(boundary.getBounds(), { padding: [20, 20] });
    }

    /**
     * Add key landmarks and locations
     */
    addKeyLandmarks(map) {
        const landmarks = [
            {
                lat: 3.8480, lng: 11.5021, 
                name: "ZARA-Events HQ", 
                icon: "fas fa-calendar-star",
                color: "#dc3545",
                description: "Event Booking Platform",
                popup: `
                    <div class="text-center">
                        <i class="fas fa-calendar-star me-2 text-danger"></i><strong>ZARA-Events</strong><br>
                        <small>Event Booking Platform</small><br>
                        <small class="text-muted">Yaoundé, Cameroon</small>
                    </div>
                `
            },
            {
                lat: 3.8667, lng: 11.5167, 
                name: "Centre Ville", 
                icon: "fas fa-building",
                color: "#28a745",
                description: "City Center",
                popup: `
                    <div class="text-center">
                        <i class="fas fa-building me-2 text-success"></i><strong>Centre Ville</strong><br>
                        <small>City Center</small>
                    </div>
                `
            },
            {
                lat: 3.8833, lng: 11.5167, 
                name: "Bastos", 
                icon: "fas fa-landmark",
                color: "#ffc107",
                description: "Diplomatic Quarter",
                popup: `
                    <div class="text-center">
                        <i class="fas fa-landmark me-2 text-warning"></i><strong>Bastos</strong><br>
                        <small>Diplomatic Quarter</small>
                    </div>
                `
            },
            {
                lat: 3.8500, lng: 11.4833, 
                name: "University Area", 
                icon: "fas fa-graduation-cap",
                color: "#6f42c1",
                description: "ICT University & Academic District",
                popup: `
                    <div class="text-center">
                        <i class="fas fa-graduation-cap me-2 text-primary"></i><strong>ICT University</strong><br>
                        <small>Academic District</small>
                    </div>
                `
            },
            {
                lat: 3.8600, lng: 11.5100, 
                name: "Government Quarter", 
                icon: "fas fa-flag",
                color: "#17a2b8",
                description: "Administrative Center",
                popup: `
                    <div class="text-center">
                        <i class="fas fa-flag me-2 text-info"></i><strong>Government Quarter</strong><br>
                        <small>Administrative Center</small>
                    </div>
                `
            }
        ];

        landmarks.forEach(landmark => {
            // Create custom icon
            const customIcon = L.divIcon({
                html: `<i class="${landmark.icon}" style="color: ${landmark.color}; font-size: 16px;"></i>`,
                iconSize: [20, 20],
                className: 'custom-landmark-icon'
            });

            L.marker([landmark.lat, landmark.lng], { icon: customIcon })
                .addTo(map)
                .bindPopup(landmark.popup);
        });
    }

    /**
     * Add service area information
     */
    addServiceAreaInfo(map) {
        // Add a subtle circle showing service radius
        L.circle([3.8480, 11.5021], {
            color: '#007bff',
            fillColor: '#007bff',
            fillOpacity: 0.05,
            radius: 15000,
            weight: 1,
            opacity: 0.3
        }).addTo(map);
    }
}

// Export for use in other scripts
window.YaoundeMap = YaoundeMap;

// Auto-initialize maps when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('YaoundeMap: DOM loaded, initializing maps...');

    try {
        const yaoundeMapInstance = new YaoundeMap();
        console.log('YaoundeMap: Instance created successfully');

        // Initialize contact page map
        const contactMapElement = document.getElementById('yaoundeMap');
        if (contactMapElement) {
            console.log('YaoundeMap: Contact map element found, initializing...');
            yaoundeMapInstance.initializeMap('yaoundeMap', {
                showBoundary: true,
                showLandmarks: true,
                showServiceArea: true
            }).then(() => {
                console.log('YaoundeMap: Contact map initialized successfully');
            }).catch(error => {
                console.error('YaoundeMap: Error initializing contact map:', error);
            });
        }

        // Initialize about page map
        const aboutMapElement = document.getElementById('developerLocationMap');
        if (aboutMapElement) {
            console.log('YaoundeMap: About map element found, initializing...');
            console.log('YaoundeMap: About map element dimensions:', aboutMapElement.offsetWidth, 'x', aboutMapElement.offsetHeight);

            yaoundeMapInstance.initializeMap('developerLocationMap', {
                center: [3.8480, 11.5021],
                zoom: 12,
                showBoundary: false,
                showLandmarks: true,
                showServiceArea: true
            }).then(() => {
                console.log('YaoundeMap: About map initialized successfully');
            }).catch(error => {
                console.error('YaoundeMap: Error initializing about map:', error);
            });
        } else {
            console.log('YaoundeMap: About map element not found');
        }
    } catch (error) {
        console.error('YaoundeMap: Error creating instance:', error);
    }
});
