/**
 * ZARA-Events PWA Features
 * Handles installation prompts, offline functionality, and app-like behaviors
 */

class PWAManager {
    constructor() {
        this.deferredPrompt = null;
        this.isInstalled = false;
        this.isOnline = navigator.onLine;
        
        this.init();
    }
    
    init() {
        this.registerServiceWorker();
        this.setupInstallPrompt();
        this.setupOfflineHandling();
        this.setupAppBehaviors();
        this.checkInstallStatus();
        this.setupPushNotifications();
    }
    
    // Register service worker
    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                const registration = await navigator.serviceWorker.register('/sw.js');
                console.log('PWA: Service Worker registered successfully');
                
                // Handle service worker updates
                registration.addEventListener('updatefound', () => {
                    const newWorker = registration.installing;
                    newWorker.addEventListener('statechange', () => {
                        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                            this.showUpdateAvailable();
                        }
                    });
                });
                
                return registration;
            } catch (error) {
                console.error('PWA: Service Worker registration failed:', error);
            }
        }
    }
    
    // Setup install prompt handling
    setupInstallPrompt() {
        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('PWA: Install prompt available');
            e.preventDefault();
            this.deferredPrompt = e;
            this.showInstallButton();
        });
        
        window.addEventListener('appinstalled', () => {
            console.log('PWA: App installed successfully');
            this.isInstalled = true;
            this.hideInstallButton();
            this.showToast('ZARA-Events installed successfully! 🎉', 'success');
        });
    }
    
    // Show install button
    showInstallButton() {
        let installBtn = document.getElementById('pwa-install-btn');
        
        if (!installBtn) {
            installBtn = document.createElement('button');
            installBtn.id = 'pwa-install-btn';
            installBtn.className = 'btn btn-primary-modern pwa-install-btn';
            installBtn.innerHTML = '<i class="fas fa-download me-2"></i>Install App';
            installBtn.onclick = () => this.promptInstall();
            
            // Add to navigation or appropriate location
            const navbar = document.querySelector('.navbar-nav');
            if (navbar) {
                const li = document.createElement('li');
                li.className = 'nav-item';
                li.appendChild(installBtn);
                navbar.appendChild(li);
            }
        }
        
        installBtn.style.display = 'block';
    }
    
    // Hide install button
    hideInstallButton() {
        const installBtn = document.getElementById('pwa-install-btn');
        if (installBtn) {
            installBtn.style.display = 'none';
        }
    }
    
    // Prompt installation
    async promptInstall() {
        if (!this.deferredPrompt) {
            this.showToast('Installation not available on this device', 'warning');
            return;
        }
        
        try {
            this.deferredPrompt.prompt();
            const { outcome } = await this.deferredPrompt.userChoice;
            
            if (outcome === 'accepted') {
                console.log('PWA: User accepted install prompt');
            } else {
                console.log('PWA: User dismissed install prompt');
            }
            
            this.deferredPrompt = null;
        } catch (error) {
            console.error('PWA: Install prompt failed:', error);
        }
    }
    
    // Setup offline handling
    setupOfflineHandling() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.showToast('Connection restored! 🌐', 'success');
            this.syncOfflineData();
        });
        
        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.showToast('You are now offline. Some features may be limited.', 'warning', 5000);
        });
        
        // Add offline indicator
        this.createOfflineIndicator();
    }
    
    // Create offline indicator
    createOfflineIndicator() {
        const indicator = document.createElement('div');
        indicator.id = 'offline-indicator';
        indicator.className = 'offline-indicator';
        indicator.innerHTML = '📡 Offline Mode';
        indicator.style.cssText = `
            position: fixed;
            top: 70px;
            right: 20px;
            background: #dc3545;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            z-index: 9999;
            display: none;
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
        `;
        
        document.body.appendChild(indicator);
        
        // Show/hide based on connection status
        if (!this.isOnline) {
            indicator.style.display = 'block';
        }
        
        window.addEventListener('online', () => {
            indicator.style.display = 'none';
        });
        
        window.addEventListener('offline', () => {
            indicator.style.display = 'block';
        });
    }
    
    // Setup app-like behaviors
    setupAppBehaviors() {
        // Prevent zoom on double tap (iOS)
        let lastTouchEnd = 0;
        document.addEventListener('touchend', (event) => {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
        
        // Handle pull-to-refresh
        this.setupPullToRefresh();
        
        // Add app-like navigation
        this.setupAppNavigation();
        
        // Handle keyboard on mobile
        this.setupMobileKeyboard();
    }
    
    // Setup pull-to-refresh
    setupPullToRefresh() {
        let startY = 0;
        let currentY = 0;
        let pullDistance = 0;
        const threshold = 100;
        
        document.addEventListener('touchstart', (e) => {
            if (window.scrollY === 0) {
                startY = e.touches[0].clientY;
            }
        });
        
        document.addEventListener('touchmove', (e) => {
            if (window.scrollY === 0 && startY) {
                currentY = e.touches[0].clientY;
                pullDistance = currentY - startY;
                
                if (pullDistance > 0) {
                    e.preventDefault();
                    // Add visual feedback here
                }
            }
        });
        
        document.addEventListener('touchend', () => {
            if (pullDistance > threshold) {
                this.refreshPage();
            }
            startY = 0;
            pullDistance = 0;
        });
    }
    
    // Refresh page
    refreshPage() {
        this.showToast('Refreshing...', 'info');
        setTimeout(() => {
            window.location.reload();
        }, 500);
    }
    
    // Setup app navigation
    setupAppNavigation() {
        // Add back button handling for standalone mode
        if (window.matchMedia('(display-mode: standalone)').matches) {
            // Add custom back button behavior
            window.addEventListener('popstate', (event) => {
                // Handle back navigation in standalone mode
                console.log('PWA: Back navigation in standalone mode');
            });
        }
    }
    
    // Setup mobile keyboard handling
    setupMobileKeyboard() {
        const viewport = document.querySelector('meta[name=viewport]');
        
        if (viewport) {
            const originalContent = viewport.content;
            
            // Adjust viewport when keyboard appears
            window.addEventListener('resize', () => {
                if (window.innerHeight < window.outerHeight * 0.75) {
                    // Keyboard is likely open
                    viewport.content = originalContent + ', user-scalable=yes';
                } else {
                    // Keyboard is likely closed
                    viewport.content = originalContent;
                }
            });
        }
    }
    
    // Check if app is installed
    checkInstallStatus() {
        // Check if running in standalone mode
        if (window.matchMedia('(display-mode: standalone)').matches || 
            window.navigator.standalone === true) {
            this.isInstalled = true;
            this.hideInstallButton();
        }
    }
    
    // Setup push notifications
    async setupPushNotifications() {
        if ('Notification' in window && 'serviceWorker' in navigator) {
            const permission = await Notification.requestPermission();
            
            if (permission === 'granted') {
                console.log('PWA: Notification permission granted');
                // Setup push subscription here if needed
            }
        }
    }
    
    // Sync offline data
    async syncOfflineData() {
        if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
            try {
                const registration = await navigator.serviceWorker.ready;
                await registration.sync.register('background-sync-cart');
                await registration.sync.register('background-sync-bookings');
                console.log('PWA: Background sync registered');
            } catch (error) {
                console.error('PWA: Background sync registration failed:', error);
            }
        }
    }
    
    // Show update available notification
    showUpdateAvailable() {
        const updateBtn = document.createElement('button');
        updateBtn.className = 'btn btn-warning btn-sm';
        updateBtn.innerHTML = 'Update Available - Refresh';
        updateBtn.onclick = () => {
            window.location.reload();
        };
        
        // Add to page or show as toast
        this.showToast('App update available! Click to refresh.', 'info', 0, updateBtn);
    }
    
    // Show toast notification
    showToast(message, type = 'info', duration = 3000, actionButton = null) {
        if (window.showToast) {
            window.showToast(message, type, duration);
        } else {
            console.log(`PWA Toast: ${type.toUpperCase()} - ${message}`);
        }
    }
    
    // Get app info
    getAppInfo() {
        return {
            isInstalled: this.isInstalled,
            isOnline: this.isOnline,
            hasServiceWorker: 'serviceWorker' in navigator,
            hasNotifications: 'Notification' in window,
            isStandalone: window.matchMedia('(display-mode: standalone)').matches
        };
    }
}

// Initialize PWA features when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.pwaManager = new PWAManager();
    console.log('PWA: Manager initialized');
});

// Export for global access
window.PWAManager = PWAManager;
