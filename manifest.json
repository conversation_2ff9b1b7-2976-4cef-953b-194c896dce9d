{"name": "ZARA-Events - Event Booking Platform", "short_name": "ZARA-Events", "description": "Discover and book amazing events in Central Africa with ZARA-Events", "start_url": "/welcome.php", "display": "standalone", "background_color": "#408681", "theme_color": "#408681", "orientation": "portrait-primary", "scope": "/", "lang": "en", "dir": "ltr", "categories": ["entertainment", "events", "booking", "lifestyle"], "icons": [{"src": "assets/images/icons/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "maskable any"}, {"src": "assets/images/icons/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "maskable any"}, {"src": "assets/images/icons/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "maskable any"}, {"src": "assets/images/icons/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "maskable any"}, {"src": "assets/images/icons/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "maskable any"}, {"src": "assets/images/icons/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable any"}, {"src": "assets/images/icons/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "maskable any"}, {"src": "assets/images/icons/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable any"}], "screenshots": [{"src": "assets/images/screenshots/desktop-home.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide", "label": "ZARA-Events Home Page"}, {"src": "assets/images/screenshots/mobile-events.png", "sizes": "390x844", "type": "image/png", "form_factor": "narrow", "label": "Browse Events on Mobile"}], "shortcuts": [{"name": "Browse Events", "short_name": "Events", "description": "Browse all available events", "url": "/events/", "icons": [{"src": "assets/images/icons/shortcut-events.png", "sizes": "96x96"}]}, {"name": "My Dashboard", "short_name": "Dashboard", "description": "View your bookings and profile", "url": "/user/dashboard.php", "icons": [{"src": "assets/images/icons/shortcut-dashboard.png", "sizes": "96x96"}]}, {"name": "Shopping Cart", "short_name": "<PERSON><PERSON>", "description": "View items in your cart", "url": "/booking/cart.php", "icons": [{"src": "assets/images/icons/shortcut-cart.png", "sizes": "96x96"}]}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}}