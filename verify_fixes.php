<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

echo "🔍 ZARA-Events System Verification\n";
echo "==================================\n\n";

$allGood = true;

// Test 1: Database Connection
echo "1. Testing Database Connection...\n";
if ($db->isConnected()) {
    echo "   ✅ Database connected successfully\n";
} else {
    echo "   ❌ Database connection failed\n";
    $allGood = false;
}

// Test 2: Check Tables
echo "\n2. Checking Database Tables...\n";
try {
    $db->query("SHOW TABLES");
    $tables = $db->resultset();
    $expectedTables = ['users', 'events', 'bookings', 'cart', 'payments', 'user_sessions', 'password_reset_tokens'];
    
    foreach ($expectedTables as $table) {
        $found = false;
        foreach ($tables as $dbTable) {
            if (in_array($table, array_values((array)$dbTable))) {
                $found = true;
                break;
            }
        }
        if ($found) {
            echo "   ✅ Table '$table' exists\n";
        } else {
            echo "   ❌ Table '$table' missing\n";
            $allGood = false;
        }
    }
} catch (Exception $e) {
    echo "   ❌ Error checking tables: " . $e->getMessage() . "\n";
    $allGood = false;
}

// Test 3: Check Users
echo "\n3. Checking User Accounts...\n";
try {
    $db->query("SELECT username, email, role FROM users");
    $users = $db->resultset();
    
    $adminFound = false;
    $testUserFound = false;
    
    foreach ($users as $user) {
        if ($user->username === 'admin') {
            $adminFound = true;
            echo "   ✅ Admin user found: {$user->email}\n";
        }
        if ($user->username === 'testuser') {
            $testUserFound = true;
            echo "   ✅ Test user found: {$user->email}\n";
        }
    }
    
    if (!$adminFound) {
        echo "   ❌ Admin user not found\n";
        $allGood = false;
    }
    if (!$testUserFound) {
        echo "   ❌ Test user not found\n";
        $allGood = false;
    }
    
    echo "   📊 Total users: " . count($users) . "\n";
} catch (Exception $e) {
    echo "   ❌ Error checking users: " . $e->getMessage() . "\n";
    $allGood = false;
}

// Test 4: Test Login Functionality
echo "\n4. Testing Login Functionality...\n";
try {
    // Test admin login
    $loginResult = $userManager->login('admin', 'admin123');
    if ($loginResult) {
        echo "   ✅ Admin login successful\n";
    } else {
        echo "   ❌ Admin login failed\n";
        $allGood = false;
    }
    
    // Clear session and test regular user
    session_destroy();
    session_start();
    
    $loginResult = $userManager->login('testuser', 'user123');
    if ($loginResult) {
        echo "   ✅ Test user login successful\n";
    } else {
        echo "   ❌ Test user login failed\n";
        $allGood = false;
    }
} catch (Exception $e) {
    echo "   ❌ Error testing login: " . $e->getMessage() . "\n";
    $allGood = false;
}

// Test 5: Check Developer Image
echo "\n5. Checking Developer Image...\n";
$imagePath = 'assets/images/IMG_8931.jpg';
if (file_exists($imagePath)) {
    echo "   ✅ Developer image found: $imagePath\n";
    $imageSize = filesize($imagePath);
    echo "   📏 Image size: " . number_format($imageSize / 1024, 2) . " KB\n";
} else {
    echo "   ❌ Developer image not found: $imagePath\n";
    $allGood = false;
}

// Test 6: Check Events Data
echo "\n6. Checking Events Data...\n";
try {
    $db->query("SELECT COUNT(*) as count FROM events WHERE status = 'active'");
    $eventCount = $db->single();
    if ($eventCount && $eventCount->count > 0) {
        echo "   ✅ Events found: {$eventCount->count} active events\n";
    } else {
        echo "   ⚠️  No active events found\n";
    }
} catch (Exception $e) {
    echo "   ❌ Error checking events: " . $e->getMessage() . "\n";
    $allGood = false;
}

// Test 7: Test Registration Function
echo "\n7. Testing Registration Function...\n";
try {
    $testData = [
        'username' => 'verify_test_' . time(),
        'email' => 'verify_' . time() . '@test.com',
        'password' => 'testpass123',
        'first_name' => 'Verify',
        'last_name' => 'Test',
        'phone' => '+237123456789',
        'address' => 'Test Address',
        'role' => 'user'
    ];
    
    $regResult = $userManager->register($testData);
    if ($regResult) {
        echo "   ✅ Registration function working\n";
        
        // Clean up test user
        $db->query("DELETE FROM users WHERE username = :username");
        $db->bind(':username', $testData['username']);
        $db->execute();
        echo "   🧹 Test user cleaned up\n";
    } else {
        echo "   ❌ Registration function failed\n";
        $allGood = false;
    }
} catch (Exception $e) {
    echo "   ❌ Error testing registration: " . $e->getMessage() . "\n";
    $allGood = false;
}

// Final Summary
echo "\n" . str_repeat("=", 50) . "\n";
echo "📊 VERIFICATION SUMMARY\n";
echo str_repeat("=", 50) . "\n";

if ($allGood) {
    echo "🎉 ALL TESTS PASSED!\n\n";
    echo "✅ Database connection: WORKING\n";
    echo "✅ User authentication: WORKING\n";
    echo "✅ Registration system: WORKING\n";
    echo "✅ Developer image: WORKING\n";
    echo "✅ Data integrity: WORKING\n\n";
    
    echo "🔗 Ready to test in browser:\n";
    echo "   • Main app: http://localhost:7823\n";
    echo "   • Registration: http://localhost:7823/auth/register.php\n";
    echo "   • Login: http://localhost:7823/auth/login.php\n";
    echo "   • About page: http://localhost:7823/about.php\n\n";
    
    echo "🔐 Test credentials:\n";
    echo "   • Admin: admin / admin123\n";
    echo "   • User: testuser / user123\n";
} else {
    echo "⚠️  SOME ISSUES DETECTED\n\n";
    echo "Please check the error messages above and resolve any issues.\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
?>
