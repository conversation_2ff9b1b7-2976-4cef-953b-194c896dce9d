# 🚀 COMPLETE FIX: ZARA-Events Deployed App Issues

## 🎯 Current Problems Identified
1. ❌ **Database Empty**: No tables or data in your deployed database
2. ❌ **Authentication Broken**: Can't register or login
3. ❌ **Developer Photo Missing**: Shows placeholder instead of your image
4. ❌ **Database Service Unknown**: Need to identify which database you're using

## 🔍 STEP 1: Identify Your Database Service

### Method A: Check Cloud Run Environment Variables
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to **Cloud Run** → **zara-events**
3. Click on your service
4. Go to **"REVISIONS"** tab
5. Click on the latest revision
6. Scroll down to **"Environment Variables"**
7. Look for these variables:
   - `DB_HOST` - This tells us which database service you're using
   - `DB_USER`, `DB_PASS`, `DB_NAME`, `DB_PORT`

### Method B: Check Your Railway Account
1. Go to [Railway.app](https://railway.app)
2. Login to your account
3. Look for a MySQL database project
4. If you find one, this is likely your database

## 🛠️ STEP 2: Fix Database Issues

### Option A: If Using Railway Database

1. **Access Railway Database**:
   - Go to [Railway.app](https://railway.app)
   - Find your MySQL database project
   - Click on the MySQL service
   - Go to **"Query"** tab

2. **Run This Complete SQL Script**:
```sql
-- ZARA-Events Database Initialization
CREATE DATABASE IF NOT EXISTS railway;
USE railway;

-- Drop existing tables if they exist (to start fresh)
DROP TABLE IF EXISTS payments;
DROP TABLE IF EXISTS password_reset_tokens;
DROP TABLE IF EXISTS user_sessions;
DROP TABLE IF EXISTS cart;
DROP TABLE IF EXISTS bookings;
DROP TABLE IF EXISTS events;
DROP TABLE IF EXISTS users;

-- Create users table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    role ENUM('user', 'admin') DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create events table
CREATE TABLE events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    event_date DATE NOT NULL,
    event_time TIME NOT NULL,
    venue VARCHAR(200) NOT NULL,
    location VARCHAR(200) NOT NULL,
    organizer VARCHAR(100) NOT NULL,
    organizer_contact VARCHAR(100),
    image_url VARCHAR(500),
    price DECIMAL(10, 2) NOT NULL,
    total_tickets INT NOT NULL,
    available_tickets INT NOT NULL,
    category VARCHAR(50),
    status ENUM('active', 'inactive', 'cancelled') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create bookings table
CREATE TABLE bookings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    event_id INT NOT NULL,
    quantity INT NOT NULL,
    total_amount DECIMAL(10, 2) NOT NULL,
    booking_status ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'pending',
    payment_status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
    booking_reference VARCHAR(50) UNIQUE NOT NULL,
    attendee_name VARCHAR(100) NOT NULL,
    attendee_email VARCHAR(100) NOT NULL,
    attendee_phone VARCHAR(20),
    special_requirements TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
);

-- Create cart table
CREATE TABLE cart (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    event_id INT NOT NULL,
    quantity INT NOT NULL,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_event (user_id, event_id)
);

-- Create user_sessions table
CREATE TABLE user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    session_token VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create password_reset_tokens table
CREATE TABLE password_reset_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_token (token),
    INDEX idx_expires (expires_at)
);

-- Create payments table
CREATE TABLE payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    booking_id INT NOT NULL,
    payment_reference VARCHAR(255) NOT NULL UNIQUE,
    payment_method ENUM('mobile_money', 'bank_transfer', 'cash', 'card') NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'XAF',
    payment_status ENUM('pending', 'processing', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    transaction_id VARCHAR(255),
    payment_gateway VARCHAR(50) DEFAULT 'simulation',
    payment_details JSON,
    processed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    INDEX idx_payment_reference (payment_reference),
    INDEX idx_payment_status (payment_status),
    INDEX idx_booking_id (booking_id)
);

-- Insert admin user (password: admin123)
INSERT INTO users (username, email, password, first_name, last_name, role) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System', 'Administrator', 'admin');

-- Insert test user (password: user123)
INSERT INTO users (username, email, password, first_name, last_name, role) VALUES
('testuser', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Test', 'User', 'user');

-- Insert sample events
INSERT INTO events (title, description, event_date, event_time, venue, location, organizer, organizer_contact, price, total_tickets, available_tickets, category, image_url) VALUES
('Tech Conference 2024', 'Annual technology conference featuring latest innovations in AI, blockchain, and cloud computing. Join industry leaders and innovators for three days of learning and networking.', '2024-03-15', '09:00:00', 'Convention Center', 'Douala, Cameroon', 'Tech Events Inc', '<EMAIL>', 175000, 500, 500, 'Technology', 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'),
('Music Festival Summer', 'Three-day music festival featuring top artists from around the world. Experience live performances, food trucks, and art installations in a beautiful outdoor setting.', '2024-06-20', '18:00:00', 'Central Park', 'Yaoundé, Cameroon', 'Music Productions', '<EMAIL>', 87500, 1000, 1000, 'Music', 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'),
('Business Workshop', 'Professional development workshop for entrepreneurs and business leaders. Learn about digital marketing, financial planning, and leadership strategies.', '2024-04-10', '10:00:00', 'Business Center', 'Libreville, Gabon', 'Business Academy', '<EMAIL>', 58500, 100, 100, 'Business', 'https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'),
('Art Exhibition', 'Contemporary art exhibition featuring works from local and international artists. Explore diverse mediums including paintings, sculptures, and digital art.', '2024-05-05', '14:00:00', 'Art Gallery', 'Bangui, Central African Republic', 'Art Collective', '<EMAIL>', 14500, 200, 200, 'Art', 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'),
('Sports Championship', 'Annual sports championship finals featuring the best teams from across the region. Experience the excitement of live sports with family and friends.', '2024-07-15', '19:00:00', 'Sports Arena', 'N\'Djamena, Chad', 'Sports League', '<EMAIL>', 43750, 2000, 2000, 'Sports', 'https://images.unsplash.com/photo-1461896836934-ffe607ba8211?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'),
('Cooking Masterclass', 'Learn from professional chefs in this hands-on cooking experience. Master the art of Central African cuisine with authentic recipes and techniques.', '2024-04-25', '16:00:00', 'Culinary Institute', 'Malabo, Equatorial Guinea', 'Chef Academy', '<EMAIL>', 73000, 50, 50, 'Food', 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'),
('Photography Workshop', 'Improve your photography skills with professional photographers. Learn about composition, lighting, and post-processing techniques.', '2024-05-18', '11:00:00', 'Photo Studio', 'Brazzaville, Republic of the Congo', 'Photo Masters', '<EMAIL>', 52500, 75, 75, 'Education', 'https://images.unsplash.com/photo-1606983340126-99ab4feaa64a?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'),
('Startup Pitch Night', 'Watch innovative startups pitch their ideas to investors and industry experts. Network with entrepreneurs and learn about the latest business trends.', '2024-06-08', '19:30:00', 'Innovation Hub', 'Kinshasa, Democratic Republic of the Congo', 'Startup Community', '<EMAIL>', 20500, 150, 150, 'Business', 'https://images.unsplash.com/photo-1559136555-9303baea8ebd?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');

-- Create indexes for better performance
CREATE INDEX idx_events_date ON events(event_date);
CREATE INDEX idx_events_category ON events(category);
CREATE INDEX idx_events_status ON events(status);
CREATE INDEX idx_bookings_user ON bookings(user_id);
CREATE INDEX idx_bookings_event ON bookings(event_id);
CREATE INDEX idx_cart_user ON cart(user_id);

-- Verify data was inserted
SELECT 'Database initialization complete!' as status;
SELECT COUNT(*) as user_count FROM users;
SELECT COUNT(*) as event_count FROM events;
```

### Option B: If Using Google Cloud SQL

1. **Access Cloud SQL Console**:
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Navigate to **SQL**
   - Find your MySQL instance
   - Click **"QUERY"** or **"CONNECT"**

2. **Run the same SQL script above** (change `railway` to your database name)

### Option C: If No Database Exists - Create New Railway Database

1. **Create Railway Account/Project**:
   - Go to [Railway.app](https://railway.app)
   - Sign up or login
   - Click **"New Project"**
   - Select **"Add MySQL"**
   - Wait for deployment (2-3 minutes)

2. **Get Connection Details**:
   - Click on the MySQL service
   - Go to **"Connect"** tab
   - Copy the connection details

3. **Update Cloud Run Environment Variables**:
   - Go to Google Cloud Console → Cloud Run → zara-events
   - Click **"EDIT & DEPLOY NEW REVISION"**
   - Go to **"Variables & Secrets"** tab
   - Add/Update these variables:
     ```
     DB_HOST=containers-us-west-xxx.railway.app
     DB_USER=root
     DB_PASS=your_railway_password
     DB_NAME=railway
     DB_PORT=3306
     ```
   - Click **"DEPLOY"**

4. **Run the SQL script** in Railway's Query tab

## 🖼️ STEP 3: Fix Developer Photo Issue

The photo issue is because the image file isn't in the deployed container. Here's how to fix it:

### Method A: Use Cloud Storage URL (Recommended)
1. **Upload your photo to Google Cloud Storage**:
   - Go to Google Cloud Console → Storage
   - Create a bucket or use existing one
   - Upload `IMG_8931.jpg`
   - Make it public
   - Copy the public URL

2. **Update the about.php file** to use the cloud URL instead of local file

### Method B: Rebuild and Redeploy with Image
1. **Ensure the image is in your local project**
2. **Rebuild Docker image**:
   ```bash
   docker build -t zaramillion/zara-events:latest .
   docker push zaramillion/zara-events:latest
   ```
3. **Redeploy Cloud Run service**

## 🧪 STEP 4: Verify Everything Works

After completing the database setup:

1. **Check Database Test Page**:
   https://zara-events-************.us-central1.run.app/test-db-connection.php
   
   **Expected Result**:
   ```
   ✅ Database Connection: SUCCESS
   ✅ 7 tables listed
   👥 Users: 2
   📅 Events: 8
   ```

2. **Test Registration**:
   https://zara-events-************.us-central1.run.app/auth/register.php
   - Create a new account
   - Should work without "user already exists" error

3. **Test Login**:
   https://zara-events-************.us-central1.run.app/auth/login.php
   - Admin: `admin` / `admin123`
   - User: `testuser` / `user123`

4. **Check About Page**:
   https://zara-events-************.us-central1.run.app/about.php
   - Should show your developer photo

## 🎯 Quick Action Plan

1. **FIRST**: Check your Cloud Run environment variables to identify database service
2. **SECOND**: Access that database service and run the SQL script
3. **THIRD**: Test the database connection page
4. **FOURTH**: Test registration and login
5. **FIFTH**: Fix the photo issue

## 📞 Need Help?

If you can't identify your database service or access it:
1. Share your Cloud Run environment variables (hide passwords)
2. Check if you have a Railway account
3. I can help you create a new database if needed

**The database fix will solve 90% of your issues. Let's get that working first!**
