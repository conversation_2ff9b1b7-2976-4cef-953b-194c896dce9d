<?php
// Simple database initializer for deployed ZARA-Events app
// Access this file directly on your deployed app to initialize the database

require_once 'includes/config.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>ZARA-Events Database Initializer</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .btn { background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; margin: 10px 5px; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🚀 ZARA-Events Database Initializer</h1>
        <p>This tool will initialize your deployed database with all necessary tables and data.</p>";

// Check database connection
if (!$db->isConnected()) {
    echo "<div class='error'>❌ Database connection failed! Please check your database configuration.</div>";
    echo "<p><strong>Database Config:</strong></p>";
    echo "<ul>";
    echo "<li>Host: " . (defined('DB_HOST') ? DB_HOST : 'Not set') . "</li>";
    echo "<li>Database: " . (defined('DB_NAME') ? DB_NAME : 'Not set') . "</li>";
    echo "<li>User: " . (defined('DB_USER') ? DB_USER : 'Not set') . "</li>";
    echo "</ul>";
    echo "</div></body></html>";
    exit;
}

echo "<div class='success'>✅ Database connection successful!</div>";

// Check if initialization is requested
if (isset($_GET['action']) && $_GET['action'] === 'initialize') {
    echo "<h2>🔧 Initializing Database...</h2>";
    
    $errors = 0;
    $success = 0;
    
    // SQL statements to create tables and insert data
    $statements = [
        // Create users table
        "CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            phone VARCHAR(20),
            address TEXT,
            role ENUM('user', 'admin') DEFAULT 'user',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )",
        
        // Create events table
        "CREATE TABLE IF NOT EXISTS events (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(200) NOT NULL,
            description TEXT,
            event_date DATE NOT NULL,
            event_time TIME NOT NULL,
            venue VARCHAR(200) NOT NULL,
            location VARCHAR(200) NOT NULL,
            organizer VARCHAR(100) NOT NULL,
            organizer_contact VARCHAR(100),
            image_url VARCHAR(500),
            price DECIMAL(10, 2) NOT NULL,
            total_tickets INT NOT NULL,
            available_tickets INT NOT NULL,
            category VARCHAR(50),
            status ENUM('active', 'inactive', 'cancelled') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )",
        
        // Create bookings table
        "CREATE TABLE IF NOT EXISTS bookings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            event_id INT NOT NULL,
            quantity INT NOT NULL,
            total_amount DECIMAL(10, 2) NOT NULL,
            booking_status ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'pending',
            payment_status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
            booking_reference VARCHAR(50) UNIQUE NOT NULL,
            attendee_name VARCHAR(100) NOT NULL,
            attendee_email VARCHAR(100) NOT NULL,
            attendee_phone VARCHAR(20),
            special_requirements TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )",
        
        // Create cart table
        "CREATE TABLE IF NOT EXISTS cart (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            event_id INT NOT NULL,
            quantity INT NOT NULL,
            added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        
        // Create user_sessions table
        "CREATE TABLE IF NOT EXISTS user_sessions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            session_token VARCHAR(255) NOT NULL,
            expires_at TIMESTAMP NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        
        // Create password_reset_tokens table
        "CREATE TABLE IF NOT EXISTS password_reset_tokens (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            token VARCHAR(255) NOT NULL,
            expires_at TIMESTAMP NOT NULL,
            used BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        
        // Create payments table
        "CREATE TABLE IF NOT EXISTS payments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            booking_id INT NOT NULL,
            payment_reference VARCHAR(255) NOT NULL UNIQUE,
            payment_method ENUM('mobile_money', 'bank_transfer', 'cash', 'card') NOT NULL,
            amount DECIMAL(10, 2) NOT NULL,
            currency VARCHAR(3) DEFAULT 'XAF',
            payment_status ENUM('pending', 'processing', 'completed', 'failed', 'refunded') DEFAULT 'pending',
            transaction_id VARCHAR(255),
            payment_gateway VARCHAR(50) DEFAULT 'simulation',
            payment_details JSON,
            processed_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )"
    ];
    
    // Execute table creation statements
    foreach ($statements as $sql) {
        try {
            $db->query($sql);
            $db->execute();
            $success++;
            echo "<div class='success'>✅ Table created successfully</div>";
        } catch (Exception $e) {
            $errors++;
            echo "<div class='error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</div>";
        }
    }
    
    // Insert admin user with correct password hash
    try {
        $adminHash = password_hash('admin123', PASSWORD_DEFAULT);
        $db->query("INSERT IGNORE INTO users (username, email, password, first_name, last_name, role) VALUES ('admin', '<EMAIL>', :password, 'System', 'Administrator', 'admin')");
        $db->bind(':password', $adminHash);
        $db->execute();
        echo "<div class='success'>✅ Admin user created (admin/admin123)</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error creating admin user: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
    // Insert test user
    try {
        $userHash = password_hash('user123', PASSWORD_DEFAULT);
        $db->query("INSERT IGNORE INTO users (username, email, password, first_name, last_name, role) VALUES ('testuser', '<EMAIL>', :password, 'Test', 'User', 'user')");
        $db->bind(':password', $userHash);
        $db->execute();
        echo "<div class='success'>✅ Test user created (testuser/user123)</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error creating test user: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
    // Insert sample events
    $events = [
        ['Tech Conference 2024', 'Annual technology conference featuring latest innovations in AI, blockchain, and cloud computing.', '2024-03-15', '09:00:00', 'Convention Center', 'Douala, Cameroon', 'Tech Events Inc', '<EMAIL>', 175000, 500, 500, 'Technology', 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'],
        ['Music Festival Summer', 'Three-day music festival featuring top artists from around the world.', '2024-06-20', '18:00:00', 'Central Park', 'Yaoundé, Cameroon', 'Music Productions', '<EMAIL>', 87500, 1000, 1000, 'Music', 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'],
        ['Business Workshop', 'Professional development workshop for entrepreneurs and business leaders.', '2024-04-10', '10:00:00', 'Business Center', 'Libreville, Gabon', 'Business Academy', '<EMAIL>', 58500, 100, 100, 'Business', 'https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80']
    ];
    
    foreach ($events as $event) {
        try {
            $db->query("INSERT IGNORE INTO events (title, description, event_date, event_time, venue, location, organizer, organizer_contact, price, total_tickets, available_tickets, category, image_url) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
            $db->execute($event);
            echo "<div class='success'>✅ Event added: " . htmlspecialchars($event[0]) . "</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ Error adding event: " . htmlspecialchars($e->getMessage()) . "</div>";
        }
    }
    
    echo "<h2>🎉 Initialization Complete!</h2>";
    echo "<div class='success'>";
    echo "<h3>✅ Database Successfully Initialized</h3>";
    echo "<p><strong>Test Credentials:</strong></p>";
    echo "<ul>";
    echo "<li>Admin: admin / admin123</li>";
    echo "<li>User: testuser / user123</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>🔗 Test Your App:</h3>";
    echo "<a href='auth/register.php' class='btn'>Test Registration</a>";
    echo "<a href='auth/login.php' class='btn'>Test Login</a>";
    echo "<a href='about.php' class='btn'>Check About Page</a>";
    echo "<a href='/' class='btn btn-success'>Go to Main App</a>";
    
} else {
    // Show current status and initialization button
    echo "<h2>📊 Current Database Status</h2>";
    
    try {
        $db->query("SHOW TABLES");
        $tables = $db->resultset();
        
        if (empty($tables)) {
            echo "<div class='warning'>⚠️ No tables found. Database needs initialization.</div>";
        } else {
            echo "<div class='success'>✅ Tables found: " . count($tables) . "</div>";
            echo "<ul>";
            foreach ($tables as $table) {
                $tableName = array_values((array)$table)[0];
                echo "<li>$tableName</li>";
            }
            echo "</ul>";
        }
        
        // Check user count
        if (!empty($tables)) {
            try {
                $db->query("SELECT COUNT(*) as count FROM users");
                $userCount = $db->single();
                echo "<p>👥 Users: " . ($userCount ? $userCount->count : 0) . "</p>";
                
                $db->query("SELECT COUNT(*) as count FROM events");
                $eventCount = $db->single();
                echo "<p>📅 Events: " . ($eventCount ? $eventCount->count : 0) . "</p>";
            } catch (Exception $e) {
                echo "<div class='warning'>⚠️ Tables exist but may need data initialization.</div>";
            }
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error checking database: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
    echo "<h2>🚀 Initialize Database</h2>";
    echo "<p>Click the button below to create all necessary tables and load sample data:</p>";
    echo "<a href='?action=initialize' class='btn btn-success'>🔧 Initialize Database Now</a>";
    
    echo "<h3>📝 What this will do:</h3>";
    echo "<ul>";
    echo "<li>Create all 7 required database tables</li>";
    echo "<li>Add admin user (admin/admin123)</li>";
    echo "<li>Add test user (testuser/user123)</li>";
    echo "<li>Load sample events</li>";
    echo "<li>Fix all authentication issues</li>";
    echo "</ul>";
}

echo "</div></body></html>";
?>
