#!/bin/bash

# 🚀 ZARA-Events Quick Deployment Script
# This script deploys your app to Google Cloud Run using the existing Docker Hub image

echo "🚀 ZARA-Events Quick Deployment to Google Cloud Run"
echo "=================================================="
echo ""

# Configuration
PROJECT_ID="zara-ride"
SERVICE_NAME="zara-events"
REGION="us-central1"
IMAGE="zaramillion/zara-events:latest"

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}📋 Configuration:${NC}"
echo "   Project: $PROJECT_ID"
echo "   Service: $SERVICE_NAME"
echo "   Region: $REGION"
echo "   Image: $IMAGE"
echo ""

# Check if gcloud is available
GCLOUD_PATH="/Users/<USER>/google-cloud-sdk/bin/gcloud"
if ! command -v gcloud &> /dev/null && [ ! -f "$GCLOUD_PATH" ]; then
    echo -e "${RED}❌ Google Cloud CLI not found${NC}"
    echo "Please install it from: https://cloud.google.com/sdk/docs/install"
    exit 1
fi

# Use the full path if gcloud is not in PATH
if [ -f "$GCLOUD_PATH" ]; then
    GCLOUD="$GCLOUD_PATH"
else
    GCLOUD="gcloud"
fi

# Check authentication
echo -e "${BLUE}🔐 Checking authentication...${NC}"
if ! $GCLOUD auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo -e "${YELLOW}⚠️  Not authenticated. Please login:${NC}"
    $GCLOUD auth login
fi

# Set project
echo -e "${BLUE}🔧 Setting project...${NC}"
$GCLOUD config set project $PROJECT_ID

# Check billing
echo -e "${BLUE}💳 Checking billing status...${NC}"
if ! $GCLOUD beta billing projects describe $PROJECT_ID >/dev/null 2>&1; then
    echo -e "${RED}❌ Billing not enabled for project $PROJECT_ID${NC}"
    echo ""
    echo -e "${YELLOW}Please enable billing:${NC}"
    echo "1. Go to: https://console.cloud.google.com/billing/linkedaccount?project=$PROJECT_ID"
    echo "2. Link a billing account"
    echo "3. Run this script again"
    echo ""
    exit 1
fi

echo -e "${GREEN}✅ Billing is enabled${NC}"

# Enable APIs
echo -e "${BLUE}🔌 Enabling required APIs...${NC}"
$GCLOUD services enable run.googleapis.com cloudbuild.googleapis.com containerregistry.googleapis.com

# Deploy to Cloud Run
echo -e "${BLUE}🚀 Deploying to Cloud Run...${NC}"
echo "This may take a few minutes..."
echo ""

$GCLOUD run deploy $SERVICE_NAME \
    --image=$IMAGE \
    --platform=managed \
    --region=$REGION \
    --allow-unauthenticated \
    --port=80 \
    --memory=1Gi \
    --cpu=1 \
    --max-instances=10 \
    --timeout=300 \
    --set-env-vars="ENVIRONMENT=production,SMTP_HOST=smtp.gmail.com,SMTP_PORT=587,SMTP_USERNAME=<EMAIL>,SMTP_PASSWORD=pvjc rjit ogxg ncce,FROM_EMAIL=<EMAIL>,FROM_NAME=ZARA-Events,ADMIN_EMAIL=<EMAIL>"

if [ $? -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🎉 Deployment Successful!${NC}"

    # Get service URL
    SERVICE_URL=$($GCLOUD run services describe $SERVICE_NAME --region=$REGION --format="value(status.url)")

    echo ""
    echo -e "${GREEN}🌐 Your ZARA-Events app is live at:${NC}"
    echo -e "${BLUE}$SERVICE_URL${NC}"
    echo ""

    # Save URL to file
    echo $SERVICE_URL > deployment-url.txt
    echo -e "${BLUE}📄 URL saved to: deployment-url.txt${NC}"

    # Quick access links
    echo -e "${BLUE}📱 Quick Access Links:${NC}"
    echo "   🏠 Home:        $SERVICE_URL"
    echo "   📅 Events:      $SERVICE_URL/events/"
    echo "   👤 About:       $SERVICE_URL/about.php"
    echo "   📞 Contact:     $SERVICE_URL/contact.php"
    echo "   🗺️  Map Test:    $SERVICE_URL/test-map.php"
    echo "   ❓ Help:        $SERVICE_URL/help-center.php"
    echo ""

    # Test the deployment
    echo -e "${BLUE}🧪 Testing deployment...${NC}"
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$SERVICE_URL")
    if [ "$HTTP_CODE" = "200" ] || [ "$HTTP_CODE" = "302" ]; then
        echo -e "${GREEN}✅ Service is responding correctly!${NC}"
    else
        echo -e "${YELLOW}⚠️  Service returned HTTP $HTTP_CODE. It may still be starting up.${NC}"
    fi

    echo ""
    echo -e "${BLUE}📊 Service Details:${NC}"
    $GCLOUD run services describe $SERVICE_NAME --region=$REGION --format="table(
        metadata.name,
        status.url,
        status.conditions[0].type,
        status.conditions[0].status
    )"

    echo ""
    echo -e "${BLUE}🛠️  Management Commands:${NC}"
    echo "   View logs:    $GCLOUD run services logs read $SERVICE_NAME --region=$REGION --follow"
    echo "   Update:       $GCLOUD run services update $SERVICE_NAME --region=$REGION"
    echo "   Delete:       $GCLOUD run services delete $SERVICE_NAME --region=$REGION"
    echo ""

    echo -e "${GREEN}🎯 Deployment Complete!${NC}"
    echo -e "${BLUE}Your ZARA-Events platform is now live and ready for users!${NC}"

else
    echo ""
    echo -e "${RED}❌ Deployment failed!${NC}"
    echo "Please check the error messages above and try again."
    echo ""
    echo -e "${BLUE}Common solutions:${NC}"
    echo "1. Ensure billing is enabled"
    echo "2. Check your internet connection"
    echo "3. Verify the Docker image exists: $IMAGE"
    echo "4. Try running individual gcloud commands manually"
    exit 1
fi
