<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Map Debug - ZARA Events</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin=""/>
    
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .debug-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .map-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        #debugMap {
            height: 500px;
            width: 100%;
            border-radius: 10px;
            border: 2px solid #ddd;
        }
        
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🗺️ Map Debug Tool - ZARA Events</h1>
        
        <div class="debug-info">
            <h3>Debug Information</h3>
            <div id="debugStatus">
                <div class="status info">🔄 Initializing map debug...</div>
            </div>
        </div>
        
        <div class="map-container">
            <h3>Test Map</h3>
            <div id="debugMap"></div>
        </div>
        
        <div class="debug-info">
            <h3>Console Output</h3>
            <div id="consoleOutput" style="background: #000; color: #0f0; padding: 15px; border-radius: 5px; font-family: monospace; max-height: 200px; overflow-y: auto;">
                <div>Console output will appear here...</div>
            </div>
        </div>
    </div>

    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
            crossorigin=""></script>

    <script>
        // Debug logging
        const debugStatus = document.getElementById('debugStatus');
        const consoleOutput = document.getElementById('consoleOutput');
        
        function addStatus(message, type = 'info') {
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
            debugStatus.appendChild(statusDiv);
        }
        
        function addConsoleLog(message) {
            const logDiv = document.createElement('div');
            logDiv.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            consoleOutput.appendChild(logDiv);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        // Override console methods
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        console.log = function(...args) {
            addConsoleLog('LOG: ' + args.join(' '));
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            addConsoleLog('ERROR: ' + args.join(' '));
            originalError.apply(console, args);
        };
        
        console.warn = function(...args) {
            addConsoleLog('WARN: ' + args.join(' '));
            originalWarn.apply(console, args);
        };
        
        // Start debugging
        addStatus('🔍 Checking Leaflet library...', 'info');
        
        if (typeof L !== 'undefined') {
            addStatus('✅ Leaflet library loaded successfully', 'success');
            console.log('Leaflet version:', L.version);
            
            try {
                addStatus('🗺️ Initializing map...', 'info');
                
                // Initialize map
                const map = L.map('debugMap').setView([3.8480, 11.5021], 11);
                
                addStatus('✅ Map container initialized', 'success');
                console.log('Map initialized successfully');
                
                // Add tile layer
                addStatus('🌍 Loading map tiles...', 'info');
                
                const tileLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                    maxZoom: 18
                }).addTo(map);
                
                tileLayer.on('load', function() {
                    addStatus('✅ Map tiles loaded successfully', 'success');
                    console.log('Tiles loaded');
                });
                
                tileLayer.on('tileerror', function(e) {
                    addStatus('❌ Error loading map tiles: ' + e.error, 'error');
                    console.error('Tile error:', e);
                });
                
                // Add a test marker
                addStatus('📍 Adding test marker...', 'info');
                
                const marker = L.marker([3.8480, 11.5021]).addTo(map);
                marker.bindPopup('<b>ZARA-Events</b><br>Test marker for Yaoundé').openPopup();
                
                addStatus('✅ Test marker added', 'success');
                
                // Test API connectivity
                addStatus('🌐 Testing API connectivity...', 'info');
                
                fetch('https://nominatim.openstreetmap.org/search?q=Yaoundé&format=json&limit=1')
                    .then(response => {
                        if (response.ok) {
                            addStatus('✅ Nominatim API accessible', 'success');
                            return response.json();
                        } else {
                            throw new Error('API response not OK');
                        }
                    })
                    .then(data => {
                        console.log('Nominatim response:', data);
                        addStatus('✅ API test successful', 'success');
                    })
                    .catch(error => {
                        addStatus('❌ API test failed: ' + error.message, 'error');
                        console.error('API error:', error);
                    });
                
                // Test Overpass API
                setTimeout(() => {
                    addStatus('🔍 Testing Overpass API...', 'info');
                    
                    const query = '[out:json][timeout:10];(relation(2746229););out geom;';
                    
                    fetch('https://overpass-api.de/api/interpreter', {
                        method: 'POST',
                        body: query,
                        headers: {
                            'Content-Type': 'text/plain'
                        }
                    })
                    .then(response => {
                        if (response.ok) {
                            addStatus('✅ Overpass API accessible', 'success');
                            return response.json();
                        } else {
                            throw new Error('Overpass API response not OK');
                        }
                    })
                    .then(data => {
                        console.log('Overpass response:', data);
                        addStatus('✅ Overpass API test successful', 'success');
                        
                        if (data.elements && data.elements.length > 0) {
                            addStatus('✅ Yaoundé boundary data found', 'success');
                        } else {
                            addStatus('⚠️ No boundary data in response', 'error');
                        }
                    })
                    .catch(error => {
                        addStatus('❌ Overpass API test failed: ' + error.message, 'error');
                        console.error('Overpass error:', error);
                    });
                }, 2000);
                
            } catch (error) {
                addStatus('❌ Map initialization failed: ' + error.message, 'error');
                console.error('Map error:', error);
            }
            
        } else {
            addStatus('❌ Leaflet library not found', 'error');
            console.error('Leaflet not loaded');
        }
    </script>
</body>
</html>
