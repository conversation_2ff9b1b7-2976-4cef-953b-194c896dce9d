#!/bin/bash

# Script to copy ZARA-Events database from Docker to local MySQL
# Make sure your local MySQL server is running before executing this script

echo "🚀 ZARA-Events Database Migration Script"
echo "========================================"
echo ""

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Check if MySQL is running locally
echo -e "${YELLOW}Checking if MySQL is running locally...${NC}"
if ! pgrep -x "mysqld" > /dev/null; then
    echo -e "${RED}❌ MySQL is not running locally. Please start MySQL first.${NC}"
    echo "You can start MySQL with: brew services start mysql"
    exit 1
fi

echo -e "${GREEN}✅ MySQL is running locally${NC}"
echo ""

# Step 1: Setup local database and user
echo -e "${YELLOW}Step 1: Setting up local database and user...${NC}"
echo "Please enter your MySQL root password when prompted:"
mysql -u root -p < setup_local_mysql.sql

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Database and user created successfully${NC}"
else
    echo -e "${RED}❌ Failed to create database and user${NC}"
    exit 1
fi

echo ""

# Step 2: Import the database dump
echo -e "${YELLOW}Step 2: Importing database from Docker backup...${NC}"
echo "Please enter your MySQL root password again when prompted:"
mysql -u root -p event_booking_system < zara_events_database_backup.sql

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Database imported successfully${NC}"
else
    echo -e "${RED}❌ Failed to import database${NC}"
    exit 1
fi

echo ""

# Step 3: Verify the import
echo -e "${YELLOW}Step 3: Verifying the import...${NC}"
echo "Checking tables and data..."

mysql -u event_user -pevent_password event_booking_system -e "
SHOW TABLES;
SELECT COUNT(*) as 'Total Users' FROM users;
SELECT COUNT(*) as 'Total Events' FROM events;
SELECT COUNT(*) as 'Total Bookings' FROM bookings;
"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Database verification successful${NC}"
else
    echo -e "${RED}❌ Database verification failed${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}🎉 Database migration completed successfully!${NC}"
echo ""
echo "Database Details:"
echo "=================="
echo "Database Name: event_booking_system"
echo "Username: event_user"
echo "Password: event_password"
echo "Host: localhost"
echo "Port: 3306"
echo ""
echo "You can now update your application configuration to use the local database."
echo ""
echo "Connection string for your app:"
echo "mysql://event_user:event_password@localhost:3306/event_booking_system"
