# 🚀 ZARA-Events Deployment Guide

## Overview

This guide will help you deploy your ZARA-Events application to Google Cloud Run. Since billing needs to be enabled for your Google Cloud project, I'll provide you with step-by-step instructions to complete the deployment manually.

## 📋 Prerequisites

✅ **Completed:**
- Google Cloud CLI installed
- Google Cloud authentication completed
- Docker installed
- Project configured (zara-ride)

❌ **Needs Action:**
- Enable billing on Google Cloud project
- Fix Docker network connectivity

## 🔧 Step 1: Enable Billing

1. **Go to Google Cloud Console:**
   ```
   https://console.cloud.google.com/billing/linkedaccount?project=zara-ride
   ```

2. **Enable billing for your project:**
   - Click "Link a billing account"
   - Select or create a billing account
   - Follow the setup process

3. **Verify billing is enabled:**
   ```bash
   gcloud beta billing projects describe zara-ride
   ```

## 🐳 Step 2: Fix Docker and Build Image

### Option A: Fix Docker Network Issue
1. **Restart Docker Desktop:**
   - Close Docker Desktop completely
   - Restart Docker Desktop
   - Wait for it to fully start

2. **Test Docker connectivity:**
   ```bash
   docker run hello-world
   ```

### Option B: Use Pre-built Image (Recommended)
Since your image is already configured for Docker Hub (`zaramillion/zara-events`), you can use a pre-built image or build locally when Docker is working.

## 🚀 Step 3: Deploy to Google Cloud Run

Once billing is enabled, run the deployment script:

```bash
cd /Users/<USER>/Documents/augment-projects/Online\ Event\ Booking\ System
./deploy-to-cloud-run.sh
```

### Manual Deployment Commands

If the script doesn't work, use these manual commands:

```bash
# Set project
gcloud config set project zara-ride

# Enable APIs
gcloud services enable run.googleapis.com
gcloud services enable cloudbuild.googleapis.com
gcloud services enable containerregistry.googleapis.com

# Deploy using Docker Hub image
gcloud run deploy zara-events \
    --image=zaramillion/zara-events:latest \
    --platform=managed \
    --region=us-central1 \
    --allow-unauthenticated \
    --port=80 \
    --memory=1Gi \
    --cpu=1 \
    --max-instances=10 \
    --set-env-vars="ENVIRONMENT=production" \
    --set-env-vars="SMTP_HOST=smtp.gmail.com" \
    --set-env-vars="SMTP_PORT=587" \
    --set-env-vars="SMTP_USERNAME=<EMAIL>" \
    --set-env-vars="SMTP_PASSWORD=pvjc rjit ogxg ncce" \
    --set-env-vars="FROM_EMAIL=<EMAIL>" \
    --set-env-vars="FROM_NAME=ZARA-Events" \
    --set-env-vars="ADMIN_EMAIL=<EMAIL>"
```

## 🔄 Alternative: Docker Hub Only Deployment

If you want to deploy to Docker Hub first and then use other platforms:

```bash
# Build image (when Docker is working)
docker build -t zaramillion/zara-events:latest .

# Login to Docker Hub
docker login

# Push to Docker Hub
docker push zaramillion/zara-events:latest
```

## 🌐 Alternative Cloud Platforms

If Google Cloud Run billing is an issue, you can deploy to these platforms using your Docker image:

### 1. Railway (Free Tier Available)
```bash
# Install Railway CLI
npm install -g @railway/cli

# Login and deploy
railway login
railway init
railway up
```

### 2. Render (Free Tier Available)
1. Go to https://render.com
2. Connect your GitHub repository
3. Create a new Web Service
4. Use Docker image: `zaramillion/zara-events:latest`

### 3. Heroku (Free tier discontinued, but still available)
```bash
# Install Heroku CLI
# Create app and deploy using container registry
heroku create zara-events-app
heroku container:push web -a zara-events-app
heroku container:release web -a zara-events-app
```

## 📊 Current Status

### ✅ Completed:
- Google Cloud CLI installed and authenticated
- Project configured (zara-ride)
- Deployment script ready
- Docker image configuration ready
- Environment variables configured

### ⏳ Pending:
- Enable billing on Google Cloud project
- Fix Docker network connectivity
- Build and push Docker image
- Deploy to Cloud Run

## 🛠️ Troubleshooting

### Docker Network Issues:
```bash
# Reset Docker
docker system prune -a
# Restart Docker Desktop
# Check network connectivity
ping google.com
```

### Google Cloud Issues:
```bash
# Check authentication
gcloud auth list

# Check project
gcloud config get-value project

# Check billing
gcloud beta billing projects describe zara-ride
```

## 📞 Next Steps

1. **Enable billing** on your Google Cloud project
2. **Fix Docker connectivity** (restart Docker Desktop)
3. **Run deployment script** or use manual commands
4. **Test the deployed application**

## 🎯 Expected Result

After successful deployment, you'll get:
- **Live URL**: `https://zara-events-xxxxxxxxx-uc.a.run.app`
- **Automatic HTTPS**: Provided by Google Cloud Run
- **Scalability**: Auto-scaling based on traffic
- **Environment**: Production-ready with all features

## 📱 Features Available After Deployment

- ✅ Complete event booking system
- ✅ User authentication and registration
- ✅ Event listings with search and filters
- ✅ Shopping cart and checkout
- ✅ Payment simulation
- ✅ Booking confirmations with QR codes
- ✅ Admin panel for event management
- ✅ Email notifications
- ✅ Interactive Yaoundé maps
- ✅ Mobile-responsive design
- ✅ Help center and documentation

## 💡 Tips

1. **Start with Docker Hub deployment** if Google Cloud billing is delayed
2. **Use Railway or Render** for quick free deployment
3. **Test locally first** to ensure everything works
4. **Monitor logs** after deployment for any issues

## 🔗 Useful Links

- [Google Cloud Console](https://console.cloud.google.com)
- [Docker Hub Repository](https://hub.docker.com/r/zaramillion/zara-events)
- [Railway](https://railway.app)
- [Render](https://render.com)

---

**Need Help?** Contact: <EMAIL>
