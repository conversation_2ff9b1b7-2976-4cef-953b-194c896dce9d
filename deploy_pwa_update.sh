#!/bin/bash

# ZARA-Events PWA Deployment Script
# This script builds and deploys the PWA-enabled version to Google Cloud Run

echo "🚀 Starting ZARA-Events PWA Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID="zara-events-948831228742"
SERVICE_NAME="zara-events"
REGION="us-central1"
IMAGE_NAME="zaramillion/zara-events"
TAG="pwa-v1.0"

echo -e "${BLUE}📋 Configuration:${NC}"
echo -e "  Project ID: ${PROJECT_ID}"
echo -e "  Service: ${SERVICE_NAME}"
echo -e "  Region: ${REGION}"
echo -e "  Image: ${IMAGE_NAME}:${TAG}"
echo ""

# Step 1: Generate PWA Icons
echo -e "${YELLOW}🎨 Generating PWA Icons...${NC}"
python3 generate_pwa_icons.py
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ PWA icons generated successfully${NC}"
else
    echo -e "${RED}❌ Failed to generate PWA icons${NC}"
    exit 1
fi

# Step 2: Validate PWA files
echo -e "${YELLOW}🔍 Validating PWA files...${NC}"

required_files=(
    "manifest.json"
    "sw.js"
    "offline.html"
    "assets/js/pwa-features.js"
    "favicon.ico"
)

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        echo -e "${GREEN}✅ $file${NC}"
    else
        echo -e "${RED}❌ Missing: $file${NC}"
        exit 1
    fi
done

# Step 3: Update Dockerfile for PWA
echo -e "${YELLOW}🐳 Updating Dockerfile for PWA...${NC}"
cat > Dockerfile << 'EOF'
FROM php:8.1-apache

# Install system dependencies
RUN apt-get update && apt-get install -y \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    libzip-dev \
    unzip \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) gd pdo pdo_mysql zip

# Enable Apache modules
RUN a2enmod rewrite headers

# Set working directory
WORKDIR /var/www/html

# Copy application files
COPY . .

# Set proper permissions
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html

# Configure Apache for PWA
RUN echo '<Directory /var/www/html>' >> /etc/apache2/apache2.conf \
    && echo '    Options Indexes FollowSymLinks' >> /etc/apache2/apache2.conf \
    && echo '    AllowOverride All' >> /etc/apache2/apache2.conf \
    && echo '    Require all granted' >> /etc/apache2/apache2.conf \
    && echo '</Directory>' >> /etc/apache2/apache2.conf

# Add PWA-specific headers
RUN echo 'Header always set X-Content-Type-Options nosniff' >> /etc/apache2/apache2.conf \
    && echo 'Header always set X-Frame-Options DENY' >> /etc/apache2/apache2.conf \
    && echo 'Header always set X-XSS-Protection "1; mode=block"' >> /etc/apache2/apache2.conf

# Expose port
EXPOSE 80

# Start Apache
CMD ["apache2-foreground"]
EOF

echo -e "${GREEN}✅ Dockerfile updated for PWA${NC}"

# Step 4: Create .htaccess for PWA
echo -e "${YELLOW}⚙️ Creating .htaccess for PWA...${NC}"
cat > .htaccess << 'EOF'
# PWA Configuration
RewriteEngine On

# Force HTTPS (if available)
RewriteCond %{HTTP:X-Forwarded-Proto} !https
RewriteCond %{HTTPS} off
RewriteRule ^ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Service Worker
<Files "sw.js">
    Header set Cache-Control "no-cache, no-store, must-revalidate"
    Header set Pragma "no-cache"
    Header set Expires 0
    Header set Service-Worker-Allowed "/"
</Files>

# Manifest
<Files "manifest.json">
    Header set Content-Type "application/manifest+json"
    Header set Cache-Control "public, max-age=604800"
</Files>

# PWA Icons
<FilesMatch "\.(png|ico|svg)$">
    Header set Cache-Control "public, max-age=2592000"
</FilesMatch>

# Security Headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# MIME Types
AddType application/manifest+json .webmanifest
AddType application/manifest+json .json
EOF

echo -e "${GREEN}✅ .htaccess created for PWA${NC}"

# Step 5: Build Docker image
echo -e "${YELLOW}🔨 Building Docker image...${NC}"
docker build -t ${IMAGE_NAME}:${TAG} .
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Docker image built successfully${NC}"
else
    echo -e "${RED}❌ Failed to build Docker image${NC}"
    exit 1
fi

# Step 6: Push to Docker Hub
echo -e "${YELLOW}📤 Pushing to Docker Hub...${NC}"
docker push ${IMAGE_NAME}:${TAG}
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Image pushed to Docker Hub${NC}"
else
    echo -e "${RED}❌ Failed to push to Docker Hub${NC}"
    exit 1
fi

# Step 7: Deploy to Cloud Run
echo -e "${YELLOW}☁️ Deploying to Google Cloud Run...${NC}"
gcloud run deploy ${SERVICE_NAME} \
    --image=${IMAGE_NAME}:${TAG} \
    --platform=managed \
    --region=${REGION} \
    --allow-unauthenticated \
    --memory=512Mi \
    --cpu=1 \
    --max-instances=10 \
    --set-env-vars="DB_HOST=hopper.proxy.rlwy.net:46358,DB_NAME=railway,DB_USER=root,DB_PASS=lrQhHqRUqUoOfphwkfLlFeJRYNCNJkzc" \
    --project=${PROJECT_ID}

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Successfully deployed to Cloud Run${NC}"
    
    # Get the service URL
    SERVICE_URL=$(gcloud run services describe ${SERVICE_NAME} --region=${REGION} --format='value(status.url)' --project=${PROJECT_ID})
    
    echo ""
    echo -e "${GREEN}🎉 PWA Deployment Complete!${NC}"
    echo -e "${BLUE}📱 Your PWA is now available at:${NC}"
    echo -e "${YELLOW}   ${SERVICE_URL}${NC}"
    echo ""
    echo -e "${BLUE}📋 PWA Features Added:${NC}"
    echo -e "   ✅ App Installation Prompt"
    echo -e "   ✅ Offline Functionality"
    echo -e "   ✅ Service Worker Caching"
    echo -e "   ✅ Push Notifications Ready"
    echo -e "   ✅ Mobile-Optimized Experience"
    echo -e "   ✅ App-like Navigation"
    echo ""
    echo -e "${BLUE}📱 To test PWA features:${NC}"
    echo -e "   1. Open ${SERVICE_URL} on mobile"
    echo -e "   2. Look for 'Install App' prompt"
    echo -e "   3. Add to home screen"
    echo -e "   4. Test offline functionality"
    echo ""
else
    echo -e "${RED}❌ Failed to deploy to Cloud Run${NC}"
    exit 1
fi
