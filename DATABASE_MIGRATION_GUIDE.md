# ZARA-Events Database Migration Guide
## Copy Database from Docker to Local MySQL

This guide will help you copy your ZARA-Events database from Docker to your local MySQL installation so your deployed application can use it.

## 📋 Prerequisites

- ✅ Docker containers are running (MySQL container: `event_booking_mysql`)
- ✅ Local MySQL is installed on your MacBook
- ✅ Database backup has been created: `zara_events_database_backup.sql`

## 🚀 Quick Start

### Step 1: Run the Migration Script

```bash
./copy_database_to_local.sh
```

This script will:
1. Create the database and user in your local MySQL
2. Import all data from the Docker database
3. Verify the migration was successful

**You'll need to enter your MySQL root password twice during this process.**

### Step 2: Verify the Migration

After running the script, you should see:
- Database: `event_booking_system` created
- User: `event_user` with password `event_password`
- All tables and data imported successfully

## 🔧 Manual Steps (if needed)

### Create Database and User Manually

```bash
mysql -u root -p < setup_local_mysql.sql
```

### Import Database Manually

```bash
mysql -u root -p event_booking_system < zara_events_database_backup.sql
```

### Test Connection

```bash
mysql -u event_user -pevent_password event_booking_system -e "SHOW TABLES;"
```

## 🌐 Connecting Your Deployed App to Local MySQL

### Option 1: Direct Connection (Requires Network Setup)

1. **Find your local IP address:**
   ```bash
   ./get_local_ip.sh
   ```

2. **Configure MySQL for remote access:**
   ```bash
   # Edit MySQL config
   sudo nano /opt/homebrew/etc/my.cnf
   
   # Add this line:
   bind-address = 0.0.0.0
   
   # Restart MySQL
   brew services restart mysql
   ```

3. **Create remote user:**
   ```bash
   mysql -u root -p -e "CREATE USER 'event_user'@'%' IDENTIFIED BY 'event_password';"
   mysql -u root -p -e "GRANT ALL PRIVILEGES ON event_booking_system.* TO 'event_user'@'%';"
   mysql -u root -p -e "FLUSH PRIVILEGES;"
   ```

4. **Update your deployed app configuration:**
   - Host: `YOUR_LOCAL_IP_ADDRESS` (from step 1)
   - Port: `3306`
   - Database: `event_booking_system`
   - Username: `event_user`
   - Password: `event_password`

### Option 2: Using ngrok (Recommended for Testing)

1. **Install ngrok:**
   ```bash
   brew install ngrok
   ```

2. **Expose your MySQL port:**
   ```bash
   ngrok tcp 3306
   ```

3. **Use the ngrok URL in your deployed app configuration**

### Option 3: Cloud Database (Recommended for Production)

Consider migrating to a cloud database service like:
- Google Cloud SQL
- AWS RDS
- Railway
- PlanetScale

## 📊 Database Information

### Connection Details
- **Database Name:** `event_booking_system`
- **Username:** `event_user`
- **Password:** `event_password`
- **Host:** `localhost` (local) or your IP address (remote)
- **Port:** `3306`

### Tables Included
- `users` - User accounts and authentication
- `events` - Event listings and details
- `bookings` - User bookings and reservations
- `cart` - Shopping cart items
- `payments` - Payment records
- `user_sessions` - Session management
- `password_reset_tokens` - Password reset functionality

### Sample Data
- Admin user: `<EMAIL>` (password: `admin123`)
- Test user: `<EMAIL>` (password: `user123`)
- 8 sample events with various categories

## 🔒 Security Considerations

⚠️ **Important Security Notes:**

1. **Firewall:** Ensure your firewall allows connections on port 3306 only from trusted sources
2. **VPN:** Consider using a VPN for secure remote database access
3. **Temporary Access:** Only enable remote access temporarily for testing
4. **Production:** Use a proper cloud database service for production deployments

## 🧪 Testing the Connection

### Test Local Connection
```bash
mysql -u event_user -pevent_password -h localhost event_booking_system -e "SELECT COUNT(*) FROM events;"
```

### Test Remote Connection (from another machine)
```bash
mysql -u event_user -pevent_password -h YOUR_IP_ADDRESS event_booking_system -e "SELECT COUNT(*) FROM events;"
```

## 📞 Troubleshooting

### Common Issues

1. **Connection Refused:**
   - Check if MySQL is running: `brew services list | grep mysql`
   - Start MySQL: `brew services start mysql`

2. **Access Denied:**
   - Verify user exists: `mysql -u root -p -e "SELECT User, Host FROM mysql.user WHERE User='event_user';"`
   - Check permissions: `mysql -u root -p -e "SHOW GRANTS FOR 'event_user'@'localhost';"`

3. **Can't Connect Remotely:**
   - Check MySQL bind-address configuration
   - Verify firewall settings
   - Ensure user has '%' host permission

### Getting Help

If you encounter issues:
1. Check the MySQL error log: `tail -f /opt/homebrew/var/mysql/*.err`
2. Verify your local IP: `./get_local_ip.sh`
3. Test local connection first before attempting remote connections

## ✅ Success Verification

Your migration is successful when:
- ✅ Local MySQL contains `event_booking_system` database
- ✅ All tables are present and populated
- ✅ You can connect using `event_user` credentials
- ✅ Your deployed app can connect to the local database

---

**Next Steps:** Once your database is migrated and accessible, update your deployed application's environment variables or configuration to point to your local MySQL database.
